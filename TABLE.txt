-- Create table
create table NUROUTP.NUROUTP_EXECUTE_STANDARD
(
  serial_no             VARCHAR2(10) not null,
  presc_no              NUMBER(10),
  item_no               NUMBER(3) not null,
  execute_no            VARCHAR2(30) not null,
  item_class            VARCHAR2(20),
  visit_date            DATE,
  visit_no              NUMBER(8),
  schedule_perform_time DATE,
  clinic_serial_no      VARCHAR2(16),
  patient_id            VARCHAR2(20),
  clinic_no             VARCHAR2(20) not null,
  dept_code             VARCHAR2(8),
  doctor                VARCHAR2(64),
  doctor_no             VARCHAR2(8),
  order_date            DATE,
  group_no              NUMBER(2),
  item_code             VARCHAR2(20) not null,
  item_name             VARCHAR2(100),
  item_spec             VARCHAR2(20),
  firm_id               VARCHAR2(20),
  amount                NUMBER(10,4),
  order_no              NUMBER(2) not null,
  units                 VARCHAR2(20),
  nurse_administration  VARCHAR2(20),
  nurse_dosage          NUMBER(10,4),
  nurse_frequency       VARCHAR2(20),
  doctor_administration VARCHAR2(20),
  doctor_frequency      VARCHAR2(20),
  doctor_dosage         NUMBER(10,4),
  dosage_units          VARCHAR2(20),
  abidance              VARCHAR2(20),
  doctor_note           VARCHAR2(80),
  create_nurse          VARCHAR2(16),
  create_time           DATE,
  which_day             NUMBER(10),
  perform_times         VARCHAR2(2) not null,
  print_nurse           VARCHAR2(16),
  print_time            DATE,
  print_status          VARCHAR2(20),
  reprint_nurse         VARCHAR2(16),
  reprint_time          DATE,
  liquor_nurse          VARCHAR2(16),
  liquor_time           DATE,
  liquor_nurse_record   VARCHAR2(16),
  start_nurse           VARCHAR2(16),
  start_time            DATE,
  end_nurse             VARCHAR2(16),
  end_time              DATE,
  execute_status        VARCHAR2(20),
  abort_indicator       VARCHAR2(20),
  abort_reason          VARCHAR2(100),
  selected              VARCHAR2(1),
  skin_result           VARCHAR2(10),
  frequency             VARCHAR2(16),
  performed_by          VARCHAR2(8),
  appoint_no            VARCHAR2(18),
  appoint_item_no       NUMBER(2),
  total                 VARCHAR2(10),
  curr_count            VARCHAR2(10),
  charges               NUMBER(12,4),
  class_name            VARCHAR2(20),
  execute_nurse_no      VARCHAR2(16),
  execute_type          VARCHAR2(1) not null,
  order_sub_no          NUMBER(4) not null
)
tablespace TSP_NUROUTP
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 32K
    next 32K
    minextents 1
    maxextents unlimited
  );
-- Add comments to the table 
comment on table NUROUTP.NUROUTP_EXECUTE_STANDARD
  is '处方和处置拆分执行表';
-- Add comments to the columns 
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.serial_no
  is '流水号，对应OUTP_PRESC.SERIAL_NO';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.presc_no
  is '处方号，对应OUTP_PRESC.PRESC_NO';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.item_no
  is '项目序号，对应OUTP_PRESC.ITEM_NO';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.execute_no
  is '执行号';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.item_class
  is '项目类别，见CLINIC_ITEM_CLASS_DICT';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.visit_date
  is '就诊日期，对应OUTP_PRESC.VISIT_DATE';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.visit_no
  is '就诊序号，对应OUTP_PRESC.VISIT_NO';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.schedule_perform_time
  is '计划执行时间,精确时间';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.clinic_serial_no
  is '诊疗项目流水号';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.patient_id
  is '患者ID';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.clinic_no
  is '门诊号，对应OUTP_ORDERS_STANDARD.CLINIC_NO';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.dept_code
  is '科室代码';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.doctor
  is '开单医生姓名';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.doctor_no
  is '开单医生代码(staff_dict.user_name)';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.order_date
  is '开单日期（OUTP_ORDERS.ORDER_DATE）';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.group_no
  is '组号';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.item_code
  is '项目代码';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.item_name
  is '项目名称';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.item_spec
  is '项目规格';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.firm_id
  is '药品厂家';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.amount
  is '数量';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.order_no
  is '医嘱组别，对应OUTP_ORDERS_STANDARD.ORDER_NO';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.units
  is '单位';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.nurse_administration
  is '护士用药途径';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.nurse_dosage
  is '护士单次剂量';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.nurse_frequency
  is '护士用药频次';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.doctor_administration
  is '医生用药途径';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.doctor_frequency
  is '医生用药频次';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.doctor_dosage
  is '医生单次剂量';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.dosage_units
  is '单次剂量单位';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.abidance
  is '天数';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.doctor_note
  is '医生说明';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.create_nurse
  is '拆分护士';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.create_time
  is '拆分时间';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.which_day
  is '第几天';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.perform_times
  is '当天的执行次数，比如一天1次，一天2次，分别为1 2';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.print_nurse
  is '打印护士';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.print_time
  is '打印时间';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.print_status
  is '打印状态（0未打印 1 已打印）';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.reprint_nurse
  is '补打印护士';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.reprint_time
  is '补打印时间';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.liquor_nurse
  is '配液护士，用于门诊输液';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.liquor_time
  is '配液时间，用于门诊输液';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.liquor_nurse_record
  is '配液记录护士，用于门诊输液';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.start_nurse
  is '开始执行护士';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.start_time
  is '开始执行时间';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.end_nurse
  is '结束输液护士，输液使用';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.end_time
  is '结束输液时间，输液使用';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.execute_status
  is '执行状态（0-未完成，1-已完成）';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.abort_indicator
  is '是否正常执行，0-异常、1-正常';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.abort_reason
  is '异常原因';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.selected
  is '选择状态(临时取值不存库，仅用于展示)';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.skin_result
  is '皮试结果(临时取值不存库，仅用于展示)';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.frequency
  is '频次';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.performed_by
  is '执行科室';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.appoint_no
  is '申请号';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.appoint_item_no
  is '申请明细号';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.total
  is '执行总次数';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.curr_count
  is '当前执行次数';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.charges
  is '金额';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.class_name
  is '类别名称';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.execute_nurse_no
  is '执行护士工号';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.execute_type
  is '处方处置类别（1-药品 2-治疗）';
comment on column NUROUTP.NUROUTP_EXECUTE_STANDARD.order_sub_no
  is '医嘱子序号，对应OUTP_ORDERS_STANDARD.ORDER_SUB_NO';
-- Create/Recreate primary, unique and foreign key constraints 
alter table NUROUTP.NUROUTP_EXECUTE_STANDARD
  add constraint PK_NUROUTP_EXECUTE_STANDARD primary key (CLINIC_NO, ORDER_NO, ORDER_SUB_NO, EXECUTE_NO, ITEM_CODE)
  using index
  tablespace TSP_NUROUTP
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 32K
    next 32K
    minextents 1
    maxextents unlimited
  );
-- Grant/Revoke object privileges 
grant select on NUROUTP.NUROUTP_EXECUTE_STANDARD to PUBLIC;

-- Create table
create table OUTPDOCT.OUTP_ORDERS_STANDARD
(
  patient_id                    VARCHAR2(20),
  clinic_no                     VARCHAR2(20) not null,
  order_no                      NUMBER(4) not null,
  order_sub_no                  NUMBER(4) not null,
  visit_date                    DATE,
  visit_no                      NUMBER(8),
  ob_visit_id                   NUMBER(8),
  serial_no                     VARCHAR2(30) not null,
  outp_serial_no                VARCHAR2(30),
  order_code                    VARCHAR2(20),
  order_text                    VARCHAR2(100),
  order_class                   VARCHAR2(1),
  amount                        NUMBER(8,2),
  frequency                     VARCHAR2(16),
  administration                VARCHAR2(16),
  dosage_units                  VARCHAR2(8),
  dosage                        NUMBER(12,4),
  repetition                    NUMBER(2),
  units                         VARCHAR2(8),
  firm_id                       VARCHAR2(30),
  item_spec                     VARCHAR2(50),
  costs                         NUMBER(20,6),
  charges                       NUMBER(20,6),
  ordered_by                    VARCHAR2(8),
  doctor                        VARCHAR2(64),
  order_date                    DATE default sysdate,
  doctor_no                     VARCHAR2(8),
  performed_by                  VARCHAR2(20),
  diagnosis_desc                VARCHAR2(160),
  presc_psno                    NUMBER(1),
  appoint_no                    VARCHAR2(32),
  rcpt_no                       VARCHAR2(20),
  presc_attr                    VARCHAR2(20),
  split_flag                    NUMBER(1),
  skin_result                   VARCHAR2(1),
  freq_detail                   VARCHAR2(80),
  perform_times                 NUMBER(3),
  abidance                      NUMBER(3),
  charge_indicator              NUMBER(1),
  nurse                         VARCHAR2(8),
  batch_no                      VARCHAR2(16),
  usage_desc                    VARCHAR2(120),
  clinic_specific               VARCHAR2(120),
  decoction                     VARCHAR2(20),
  count_per_repetition          NUMBER(2),
  execute_nurse                 VARCHAR2(16),
  execute_time                  VARCHAR2(20),
  ordered_nurse                 VARCHAR2(16),
  check_flag                    VARCHAR2(1),
  treat_item                    VARCHAR2(1),
  skin_flag                     NUMBER(1),
  signature_no                  NUMBER(8),
  getdrug_flag                  VARCHAR2(2),
  dose_per_unit                 NUMBER(12,4),
  bjca_cn                       VARCHAR2(30),
  bjca_value                    VARCHAR2(3000),
  bjca_time                     VARCHAR2(3000),
  his_unit_code                 VARCHAR2(30),
  tj_lsh                        VARCHAR2(20),
  trade_price                   NUMBER(20,6),
  batch_code                    VARCHAR2(20),
  guid                          VARCHAR2(50),
  item_price                    NUMBER(20,6),
  ordered_nurse_no              VARCHAR2(16),
  execute_status                VARCHAR2(1),
  recipetype                    VARCHAR2(64),
  print_status                  VARCHAR2(1) default 0,
  online_presc_no               VARCHAR2(32),
  item_no                       NUMBER(3),
  herbal_extract_juice_quantity NUMBER(8,4),
  herbal_dosage                 NUMBER(8,4),
  herbal_rules_of_treatment     VARCHAR2(200),
  cpresc_name                   VARCHAR2(40),
  ismbcf                        VARCHAR2(1),
  insur_code                    VARCHAR2(132),
  insur_name                    VARCHAR2(554),
  drug_spec                     VARCHAR2(50),
  reason_for_medication         VARCHAR2(500),
  presc_comm_taboo              VARCHAR2(400),
  special_request               VARCHAR2(10),
  zjgh                          VARCHAR2(1),
  phyexam_no                    VARCHAR2(100),
  opsp_dise_code                VARCHAR2(20)
)
tablespace TSP_OUTPADM
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 32K
    next 8K
    minextents 1
    maxextents unlimited
  );
-- Add comments to the table 
comment on table OUTPDOCT.OUTP_ORDERS_STANDARD
  is '门诊医嘱主记录';
-- Add comments to the columns 
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.patient_id
  is '病人标识号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.clinic_no
  is '门诊号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.order_no
  is '医嘱序号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.order_sub_no
  is '医嘱子序号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.visit_date
  is '就诊日期';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.visit_no
  is '就诊序号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.ob_visit_id
  is '急诊留观就诊次数';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.serial_no
  is '流水号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.outp_serial_no
  is '对应医生一次开单';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.order_code
  is '医嘱代码';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.order_text
  is '医嘱正文诊疗项目';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.order_class
  is '医嘱类别：来源ORDER_CLASS_DICT诊疗项目';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.amount
  is '数量(处置项目直接输入)';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.frequency
  is '频次 药品必须';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.administration
  is '给药途径和方法';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.dosage_units
  is '剂量单位';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.dosage
  is '药品一次使用剂量';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.repetition
  is '药品剂数';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.units
  is '药品包装单位';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.firm_id
  is '厂家标识';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.item_spec
  is '规格,药品不包含厂家';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.costs
  is '应收款';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.charges
  is '实收费用';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.ordered_by
  is '开单科室';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.doctor
  is '开单医生姓名';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.order_date
  is '开单日期';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.doctor_no
  is '开单医生代码(staff_dict.user_name)';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.performed_by
  is '执行科室';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.diagnosis_desc
  is '门诊诊断';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.presc_psno
  is '批次号（有皮试结果时回写该字段）';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.appoint_no
  is '检查检验手术用血申请单号 处方号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.rcpt_no
  is '收据号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.presc_attr
  is '处方属性 门诊处方 精神一 毒麻';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.split_flag
  is '拆包用药标志，1-拆包，0-不拆包 药品字段';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.skin_result
  is '皮试结果（0-无结果；1-阴性-；2-弱陽性+；3-陽性++；4-中陽性+++；5-强陽性++++；9-持续用药） ';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.freq_detail
  is '医生说明';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.perform_times
  is '药品 执行次数';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.abidance
  is '用药天数';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.charge_indicator
  is '收费标记 更新,在医生站界面判断状态用';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.nurse
  is '护士登录名(staff_dict.user_name)';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.batch_no
  is '药品批次';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.usage_desc
  is '药方用法';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.clinic_specific
  is '不使用';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.decoction
  is '是否代煎';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.count_per_repetition
  is '每剂煎几份';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.execute_nurse
  is '执行护士';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.execute_time
  is '护士执行时间';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.ordered_nurse
  is '开单护士';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.check_flag
  is '空或0未审核，1已审核(医保审核用)';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.treat_item
  is '治疗项目 1-是，0-否';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.skin_flag
  is '皮试标志（0-空；1-皮试；2-续注；3-免皮试）';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.signature_no
  is '电子签章序号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.getdrug_flag
  is '取药标志';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.dose_per_unit
  is '最小单位剂量 中草药计算数量必须';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.bjca_cn
  is 'ca唯一标识序列';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.bjca_value
  is '签名值字符串';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.bjca_time
  is '时间戳';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.his_unit_code
  is '医院编码(hospital_config.his_unit_code)';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.tj_lsh
  is '体检流水号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.trade_price
  is '进价';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.batch_code
  is '药品批号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.item_price
  is '单价';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.ordered_nurse_no
  is '开单护士编号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.execute_status
  is '执行状态（0为已执行，1为取消执行）';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.online_presc_no
  is '互联网医院线上处方号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.item_no
  is '顺序号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.herbal_extract_juice_quantity
  is '草药取汁量（ml/剂）';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.herbal_dosage
  is '草药单次服用量单位（ml/每次）';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.herbal_rules_of_treatment
  is '中药治则';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.cpresc_name
  is '中药处方名称';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.ismbcf
  is '是否慢病处方';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.insur_code
  is '国家编码';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.insur_name
  is '国家名称';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.drug_spec
  is '规格';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.presc_comm_taboo
  is '草药处方禁忌';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.special_request
  is '草药特殊要求：烘烤，粉碎';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.zjgh
  is '诊间挂号标志 1为诊间挂号， null或其他为非诊间挂号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.phyexam_no
  is '体检号';
comment on column OUTPDOCT.OUTP_ORDERS_STANDARD.opsp_dise_code
  is '慢特病种';
-- Create/Recreate indexes 
create index OUTPDOCT.IDX_OUTP_ORDERS_STANDARD_APPNO on OUTPDOCT.OUTP_ORDERS_STANDARD (APPOINT_NO)
  tablespace TSP_OUTPADM
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 32K
    next 32K
    minextents 1
    maxextents unlimited
  );
create index OUTPDOCT.OUTP_ORDERS_STANDARD_PID on OUTPDOCT.OUTP_ORDERS_STANDARD (PATIENT_ID)
  tablespace TSP_OUTPADM
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 32K
    next 32K
    minextents 1
    maxextents unlimited
  );
create index OUTPDOCT.OUTP_ORDERS_STANDARD_VISIT_IND on OUTPDOCT.OUTP_ORDERS_STANDARD (VISIT_DATE, VISIT_NO)
  tablespace TSP_OUTPADM
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 32K
    next 32K
    minextents 1
    maxextents unlimited
  );
-- Create/Recreate primary, unique and foreign key constraints 
alter table OUTPDOCT.OUTP_ORDERS_STANDARD
  add constraint PK_OUTP_ORDERS_STANDARD1 primary key (CLINIC_NO, ORDER_NO, ORDER_SUB_NO)
  using index
  tablespace TSP_OUTPADM
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 32K
    next 32K
    minextents 1
    maxextents unlimited
  );
-- Grant/Revoke object privileges 
grant select on OUTPDOCT.OUTP_ORDERS_STANDARD to PUBLIC;
