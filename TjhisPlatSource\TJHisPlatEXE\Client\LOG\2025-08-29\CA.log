2025-08-29 18:05:02
update staff_dict CA_ID，CA_ID:1002020325082116d28bc990349cf0169857;certSN:1002020325082116d28bc990349cf0169857
-----------------------------------
2025-08-29 18:05:02
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:02
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:02
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:02
userAuth，url:http://***********:8091/doctor/api/v1.0/auth/oauth?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"userId":"ADMIN","oauthMethod":"3","callbackURL":"","redirectURL":"","imageType":"","isBackSignatureImg":""}
-----------------------------------
2025-08-29 18:05:02
userAuth，code:0;reslut:{"status":"0","message":"success","data":{"transactionId":"7iaq3y9xly7s8cnw","oauthMPCode":"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","oauthWindowURL":"http://***********:8091/h5/authwindow/index.html?t=7iaq3y9xly7s8cnw"}}
-----------------------------------
2025-08-29 18:05:02
userAuth，sql:insert into MEDREC.CA_LOGIN_LOG(APPLICATIONS,USER_ID,LOGIN_DATE,LOGIN_mothed,CLIENT_NAME,IP_ADDRESS,transactionId) 
                                                values ('OUTPDOCT','ADMIN',sysdate,'2','ZC','***********','7iaq3y9xly7s8cnw')
-----------------------------------
2025-08-29 18:05:03
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:03
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:03
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:03
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:03
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:03
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:03
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:03
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:03
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:03
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:03
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:03
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:03
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:03
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:04
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:04
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:04
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:04
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:04
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:04
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:04
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:04
LogIn，DialogResult:Cancel
-----------------------------------
2025-08-29 18:05:05
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:05
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:05
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:05
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:05
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:05
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:05
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:05
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:05
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:05
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:05
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:06
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:06
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:06
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:06
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:06
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:06
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:06
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:07
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:07
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:07
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:07
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:07
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:07
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:07
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:07
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:07
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:07
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:07
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:07
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:07
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:07
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:08
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:08
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:08
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:08
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:08
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:08
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:08
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:08
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:08
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:08
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:08
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:08
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:08
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:08
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:09
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:09
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:09
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:09
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:09
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:09
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:09
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:09
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:09
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:09
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:09
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:09
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:09
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:09
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:10
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:10
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:10
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:10
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:10
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:10
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:10
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:10
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:10
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:10
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:10
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:10
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:10
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:10
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:11
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:11
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:11
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:11
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:11
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:11
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:11
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:11
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:11
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:11
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:11
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:11
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:11
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:11
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:12
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:12
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:12
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:12
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:12
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:12
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:12
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:12
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:12
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:12
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:12
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:12
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:12
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:12
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:13
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:13
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:13
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:13
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:13
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:13
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:13
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:13
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:13
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:13
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:13
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:13
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:13
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:13
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:14
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:14
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:14
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:14
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:14
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:14
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:14
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:14
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:14
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:14
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:14
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:14
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:14
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:14
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:15
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:15
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:15
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:15
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:15
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:15
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:15
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:15
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:15
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:15
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:15
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:15
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:15
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:15
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:16
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:16
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:16
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:16
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:16
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:16
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:16
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:16
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:16
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:16
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:16
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:16
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:16
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:16
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:17
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:17
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:17
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:17
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:17
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:17
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:17
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:17
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:17
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:17
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:17
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:17
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:17
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:17
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:18
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:18
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:18
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:18
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:18
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:18
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:18
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:18
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:18
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:18
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:18
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:18
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:18
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:18
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:19
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:19
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:19
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:19
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:19
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:19
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:19
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:19
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:19
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:19
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:19
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:19
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:19
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:19
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:20
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:20
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:20
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:20
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:20
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:20
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:20
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:20
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:20
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:20
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:20
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:20
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:20
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:20
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:21
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:21
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:21
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:21
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:21
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:21
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:21
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:21
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:21
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:21
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:21
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:21
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:21
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:21
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:22
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:22
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:22
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:22
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:22
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:22
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:22
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:22
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:22
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:22
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:22
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:22
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:22
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:22
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:23
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:23
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:23
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:23
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:23
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:23
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:23
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:23
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:23
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:23
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:23
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:23
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:23
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:23
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:24
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:24
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:24
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:24
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:24
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:24
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:24
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:24
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:24
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:24
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:24
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:24
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:24
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:24
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:25
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:25
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:25
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:25
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:25
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:25
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:25
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:25
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:25
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:25
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:25
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:25
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:25
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:25
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:26
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:26
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:26
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:26
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:26
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:26
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:26
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:26
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:26
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:26
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:26
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:26
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:26
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:26
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:27
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:27
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:27
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:27
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:27
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:27
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:27
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:27
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:27
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:27
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:27
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:27
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:27
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:27
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:28
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:28
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:28
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:28
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:28
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:28
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:28
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:28
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:28
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:28
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:28
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:28
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:28
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:28
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:29
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:29
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:29
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:29
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:29
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:29
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:29
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:29
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:29
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:29
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:29
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:29
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:29
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:29
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:30
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:30
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:30
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:30
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:30
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:30
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:30
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:30
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:30
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:30
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:30
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:30
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:30
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:30
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:31
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:31
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:31
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:31
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:31
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:31
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:31
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:31
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:31
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:31
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:31
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:31
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:31
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:31
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:32
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:32
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:32
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:32
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:32
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:32
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:32
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:32
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:32
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:32
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:32
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:32
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:32
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:32
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:33
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:33
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:33
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:33
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:33
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:33
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:33
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:33
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:33
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:33
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:33
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:33
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:33
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:33
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:34
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:34
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:34
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:34
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:34
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:34
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:34
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:34
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:34
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:34
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:34
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:34
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:34
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:34
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:35
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:35
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:35
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:35
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:35
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:35
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:35
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:35
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:35
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:35
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:35
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:35
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:35
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:35
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:36
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:36
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:36
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:36
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:36
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:36
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:36
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:36
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:36
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:36
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:36
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:36
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:36
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:36
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:37
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:37
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:37
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:37
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:37
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:37
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:37
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:37
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:37
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:37
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:37
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:37
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:37
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:37
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:38
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:38
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:38
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:38
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:38
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:38
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:38
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:38
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:38
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:38
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:38
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:38
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:38
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:38
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:39
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:39
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:39
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:39
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:39
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:39
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:39
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:39
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:39
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:39
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:39
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:39
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:39
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:39
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:40
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:40
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:40
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:40
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:40
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:40
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:40
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:40
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:40
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:40
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:40
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:40
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:40
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:40
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:41
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:41
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:41
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:41
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:41
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:41
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:41
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:41
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:41
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:41
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:41
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:41
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:41
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:41
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:42
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:42
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:42
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:42
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:42
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:42
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:42
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:42
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:42
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:42
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:42
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:42
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:42
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:42
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:43
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:43
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:43
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:43
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:43
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:43
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:43
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:43
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:43
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:43
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:43
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:43
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:43
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:43
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:44
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:44
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:44
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:44
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:44
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:44
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:44
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:44
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:44
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:44
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:44
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:44
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:44
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:44
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:45
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:45
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:45
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:45
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:45
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:45
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:45
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:45
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:45
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:45
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:45
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:45
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:45
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:45
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:46
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:46
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:46
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:46
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:46
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:46
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:46
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:46
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:46
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:46
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:46
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:46
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:46
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:46
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:47
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:47
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:47
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:47
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:47
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:47
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:47
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:47
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:47
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:47
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:47
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:47
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:47
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:47
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:48
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:48
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:48
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:48
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:48
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:48
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:48
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:48
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:48
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:48
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:48
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:48
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:48
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:48
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:49
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:49
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:49
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:49
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:49
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:49
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:49
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:49
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:49
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:49
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:49
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:49
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:49
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:49
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:50
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:50
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:50
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:50
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:50
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:50
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:50
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:50
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:50
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:50
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:50
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:50
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:50
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:50
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:51
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:51
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:51
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:51
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:51
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:51
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:51
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:51
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:51
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:51
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:51
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:51
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:51
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:51
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:52
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:52
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:52
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:52
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:52
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:52
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:52
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:52
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:52
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:53
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:53
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:53
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:53
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:53
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:53
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:53
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:53
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:53
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:54
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:54
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:54
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:54
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:54
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:54
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:54
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:54
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:54
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:54
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:54
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:54
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:54
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:54
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:55
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:55
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:55
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:55
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:55
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:55
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:55
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:55
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:55
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:55
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:55
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:55
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:55
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:55
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:56
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:56
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:56
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:56
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:56
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:56
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:56
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:56
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:56
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:56
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:56
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:56
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:56
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:56
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:57
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:57
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:57
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:57
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:57
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:57
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:57
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:57
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:57
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:57
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:57
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:57
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:57
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:57
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:58
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:58
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:58
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:58
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:58
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:58
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:58
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:58
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:58
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:58
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:58
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:58
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:58
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:58
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:59
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:59
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:05:59
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:59
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:59
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:05:59
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:05:59
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:59
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:05:59
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:05:59
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:59
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:05:59
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:06:00
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:06:00
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:06:00
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:06:00
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:06:00
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:06:00
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:06:00
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:06:00
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:06:00
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:06:00
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:06:00
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:06:00
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:06:00
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:06:00
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:06:01
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:06:01
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:06:01
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:06:01
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"7iaq3y9xly7s8cnw"}
-----------------------------------
2025-08-29 18:06:01
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:06:01
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"7iaq3y9xly7s8cnw","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:06:01
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "7iaq3y9xly7s8cnw",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
2025-08-29 18:06:01
getAccessToken，url:http://***********:8091/api/v1.0/getAccessToken;pars:{"appid":"****************","appkey":"LQO28WJ5BI7LSPF9N3D351DHTEBBJ3OV"}
-----------------------------------
2025-08-29 18:06:01
getAccessToken，code:0;reslut:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:06:01
getAccessToken，返回数据:{"status":"0","message":"success","data":{"accessToken":"01d653a728664448867a00dc305c180d_9"}}
-----------------------------------
2025-08-29 18:06:01
getOauthStatus，url:http://***********:8091/doctor/api/v1.0/auth/getOauthStatus?accessToken=01d653a728664448867a00dc305c180d_9;pars:{"transactionId":"4e6as1eq5x5sopi6"}
-----------------------------------
2025-08-29 18:06:01
getOauthStatus，code:0;reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:06:01
getOauthStatus，reslut:{"status":"0","message":"success","data":{"oauthStatus":"0","userId":"ADMIN","userNumExpands":null,"userName":null,"certDN":null,"certSN":null,"certStartTime":null,"certEndTime":null,"oauthSignature":null,"authKEY":null,"authTime":null,"expireTime":null,"authStartTime":null,"authEndTime":null,"idcard":null,"transactionId":"4e6as1eq5x5sopi6","userPhone":null,"officeName":null,"callbackURL":"","authType":null,"oauthMethod":"3","optUserId":null,"signatureImg":null,"imageUpdateTime":null,"sign":null,"officeQyId":null}}
-----------------------------------
2025-08-29 18:06:01
getOauthStatus，data:{
  "oauthStatus": "0",
  "userId": "ADMIN",
  "userNumExpands": null,
  "userName": null,
  "certDN": null,
  "certSN": null,
  "certStartTime": null,
  "certEndTime": null,
  "oauthSignature": null,
  "authKEY": null,
  "authTime": null,
  "expireTime": null,
  "authStartTime": null,
  "authEndTime": null,
  "idcard": null,
  "transactionId": "4e6as1eq5x5sopi6",
  "userPhone": null,
  "officeName": null,
  "callbackURL": "",
  "authType": null,
  "oauthMethod": "3",
  "optUserId": null,
  "signatureImg": null,
  "imageUpdateTime": null,
  "sign": null,
  "officeQyId": null
}
-----------------------------------
