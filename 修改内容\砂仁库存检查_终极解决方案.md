# 砂仁库存检查问题 - 终极解决方案

## 问题描述
1. 库存16g，第一次开10g成功
2. 第二次开12g时，系统**静默将12g改为5g**并保存（严重错误！）
3. 第三次才正确提示库存不足

## 根本原因

### 1. 异常被捕获但处理继续
- `GetAmountChangeOrders`检测到库存不足时抛出异常
- 异常被`ExcuteExcetionMethodChangeCursor`捕获并显示
- **但处理继续进行**，导致错误的值被保存

### 2. 剂量被静默修改为NORMAL_DOSAGE
- 5g来自`DRUG_RATIONAL_DOSAGE`表的`NORMAL_DOSAGE`字段
- 在`CDrugPresc.cs`第693行，系统会自动设置`orders.DOSAGE = NORMAL_DOSAGE`
- 这导致用户输入的12g被静默改为5g

## 已实施的修复

### 1. 阻止库存检查失败后的处理（UcCdrugPresc.cs）
```csharp
// 第1518-1549行
else
{
    try
    {
        if (!PrescBusiness.GetAmountChangeOrders(...))
        {
            // 库存检查失败，必须阻止后续处理
            if (!string.IsNullOrEmpty(strMsg))
            {
                // 记录错误日志
                // 抛出异常，确保上层处理停止
                throw new MessageException(strMsg);
            }
            return false;
        }
    }
    catch (MessageException)
    {
        // 重新抛出，确保错误被正确处理
        throw;
    }
}
```

### 2. 保护用户输入的剂量（CDrugPresc.cs）
```csharp
// 第693-699行
// 修复：不要自动覆盖用户输入的剂量
// 只有当剂量为0或未设置时，才使用NORMAL_DOSAGE
if (orders.DOSAGE.ToDecimal(0) == 0)
{
    orders.DOSAGE = ads[0]["NORMAL_DOSAGE"].ToDecimal(0);
}
// 否则保留用户输入的剂量值
```

### 3. 中药跳过进定销处理（CDrugPresc.cs）
```csharp
// 第2406-2414行
if (order.ORDER_CLASS == OrderClassDict.ORDER_CLASS_CHINESE_MEDICINE)
{
    WriteDebugLog($"[CDrugPresc.GetAmountChangeOrders] 中药跳过进定销处理");
    return true;
}
```

### 4. 正确的库存检查逻辑（CDrugPresc.cs）
```csharp
// 第1662-1814行
// 中药按DOSAGE * REPETITION计算
// 查询OUTP_ORDERS_STANDARD表
// 使用CHARGE_INDICATOR = 0识别未缴费处方
```

## 测试验证

### 预期行为
1. 库存16g，第一次开10g → 成功
2. 第二次开12g → **立即提示库存不足，阻止保存**
3. 用户输入的12g**不会被改为5g**

### 验证SQL
```sql
-- 检查砂仁的NORMAL_DOSAGE配置（5g的来源）
SELECT DRUG_NAME, NORMAL_DOSAGE, MAX_DOSAGE, MIN_DOSAGE
FROM DRUG_RATIONAL_DOSAGE 
WHERE DRUG_NAME LIKE '%砂仁%';

-- 查看保存的处方剂量
SELECT ITEM_NAME, DOSAGE, REPETITION, 
       DOSAGE * REPETITION as 用量,
       TO_CHAR(ORDER_DATE, 'HH24:MI:SS') as 时间
FROM OUTP_ORDERS_STANDARD
WHERE ITEM_NAME LIKE '%砂仁%'
  AND ORDER_DATE > SYSDATE - 1
ORDER BY ORDER_DATE DESC;
```

## 关键原则

1. **用户输入优先**：系统不能静默修改用户输入的剂量
2. **库存检查必须生效**：检查失败必须阻止保存
3. **错误必须明确**：不能静默处理错误
4. **DOSE_PER_UNIT保持1g**：这是最小单位，不是默认剂量

## 注意事项

- NORMAL_DOSAGE只在剂量为0时使用，不能覆盖用户输入
- 库存检查失败必须抛出异常并停止处理
- 不要让异常处理机制掩盖真正的问题

