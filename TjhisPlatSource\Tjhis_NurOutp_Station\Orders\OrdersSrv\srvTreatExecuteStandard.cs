﻿using System;
using System.Collections.Generic;
using System.Data;
//using Tjhis.HisComm;
//using SQL = Tjhis.CsComm.Cs02StringHelper;
//using Tjhis.CsComm;
using System.Collections;
using System.Text;
//using TJHIS.Base;
using SQL = PlatCommon.Base02.Cs02StringHelper;
using NM_Service.NMService;
using PlatCommon.SysBase;
using PlatCommon.Common;


namespace Tjhis.NurOutp.Station
{
   public  class srvTreatExecuteStandard
    {

       // private His01DbHelper ws = new His01DbHelper();
        private ServerPublicClient ws = new NM_Service.NMService.ServerPublicClient();
        int iDays = 0;

        #region 获得诊断列表

        /// <summary>
        /// 获得诊断列表
        /// </summary>
        /// <param name="pat_id"></param>
        /// <param name="vi_date"></param>
        /// <param name="vi_no"></param>
        /// <returns></returns>
        public DataSet GetDiagnosis(string pat_id, DateTime vi_date, string vi_no)
        {
            string sql = "select a.diag_desc as \"诊断\",a.doctor as \"医生\",a.doctor_no as \"医生编号\","
                              +@" a.ORDINAL,
                               a.VISIT_DATE,
                               a.VISIT_NO
                          from outp_mr a
                         WHERE a.patient_id ='" + pat_id
                            + "' and a.visit_date= to_date('" + vi_date.ToString()
                            + "','yyyy-MM-dd HH24:mi:ss')"
                            + " and a.visit_no = '" + vi_no
                            + "'";

            try
            {
                DataSet ds = ws.GetDataBySql(sql, "outp_mr", false);
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }

        #endregion


        #region 获得处方列表

        /// <summary>
        /// 获得处方列表
        /// </summary>
        /// <param name="ad_visit_date"></param>
        /// <param name="ai_visit_no"></param>
        /// <returns></returns>
        //public DataSet GetPresc(DateTime ad_visit_date, string ai_visit_no)
        //{
        //    //由于英文字符集数据库as 要加双引号所以拼接字符串
        //    StringBuilder sqlText = new StringBuilder(512);

        //    sqlText.AppendLine("SELECT OUTP_ORDERS_STANDARD.PRESC_NO as \"处方号\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.VISIT_DATE as \"就诊日期\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.DRUG_NAME as \"药名\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.DRUG_SPEC as \"规格\",");
        //    sqlText.AppendLine(" OUTP_ORDERS_STANDARD.FIRM_ID as \"厂家\",");
        //    sqlText.AppendLine("(CASE OUTP_ORDERS_STANDARD.SKIN_FLAG WHEN '1' THEN '是' ELSE '否' END) \"是否皮试\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.SKIN_RESULT as \"结果\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.AMOUNT as \"数量\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.UNITS as \"单位\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.REPETITION as \"剂数\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.DOSAGE as \"用量\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.DOSAGE_UNITS as \"用量单位\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.ADMINISTRATION as \"途径\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.FREQUENCY as \"频次\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.ABIDANCE as \"用药天数\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.CHARGES as \"实收\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.DEPT_NAME as \"药局\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.FREQ_DETAIL as \"医生说明\",");
        //    sqlText.AppendLine("(CASE OUTP_ORDERS_STANDARD.GETDRUG_FLAG WHEN '1' THEN '取药' ELSE '不取药' END) as \"取药属性\",");
        //    sqlText.AppendLine("OUTP_ORDERS_STANDARD.PRESC_ATTR as \"处方属性\",");
        //    sqlText.AppendLine("(CASE OUTP_ORDERS_STANDARD.CHARGE_INDICATOR WHEN 1 THEN '已收费' ELSE '未收费' END) as \"收费\",");
        //    string sql = @"
                           
        //                   '0' as del_indicator,
        //                   '普通药品' as toxi_property,
        //                   '' as official_catalog,
        //                   OUTP_ORDERS_STANDARD.VISIT_NO,
        //                   OUTP_ORDERS_STANDARD.SERIAL_NO,
        //                   OUTP_ORDERS_STANDARD.ITEM_NO,
        //                   OUTP_ORDERS_STANDARD.ITEM_CLASS,
        //                   OUTP_ORDERS_STANDARD.ITEM_CODE,
        //                   OUTP_ORDERS_STANDARD.PROVIDED_INDICATOR,
        //                   OUTP_ORDERS_STANDARD.COSTS,
        //                   OUTP_ORDERS_STANDARD.ORDER_NO,
        //                   OUTP_ORDERS_STANDARD.SUB_ORDER_NO,
        //                   OUTP_ORDERS_STANDARD.SPLIT_FLAG,
        //                   OUTP_ORDERS_STANDARD.PERFORM_TIMES
        //              FROM OUTP_ORDERS_STANDARD, CLINIC_MASTER, DEPT_DICT
        //             WHERE (OUTP_ORDERS_STANDARD.VISIT_DATE = CLINIC_MASTER.VISIT_DATE)
        //               and (OUTP_ORDERS_STANDARD.VISIT_NO = CLINIC_MASTER.VISIT_NO)
        //               and (OUTP_ORDERS_STANDARD.DISPENSARY = DEPT_DICT.DEPT_CODE)
        //                and CLINIC_MASTER.VISIT_DATE = to_date('" + ad_visit_date.ToString()
        //            + "','yyyy-MM-dd HH24:mi:ss')"
        //            + "and CLINIC_MASTER.VISIT_NO = '" + ai_visit_no
        //            + "'";

        //    try
        //    {
        //        DataSet ds = ws.GetDataBySql(sqlText.ToString()+ sql, "OUTP_ORDERS_STANDARD", false);
        //        return ds;

        //    }
        //    catch (Exception ex)
        //    {

        //        throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
        //    }
        //}

        #endregion


        #region 拆分处置

        /// <summary>
        /// 拆分处置执行
        /// </summary>
        /// <param name="strPaId">病人ID</param>
        /// <param name="strStartDt">查询日期开始</param>
        /// <param name="strEndDt">查询日期结束</param>
        /// <param name="lisAdministration">途径集合</param>
        /// <returns></returns>
        public bool splitIntoTransOrderExecute2(string strPaId, string ai_visit_no, DateTime dtStart, DateTime dtEnd, Dictionary<string, string> dictFrq, DateTime ad_visit_date)
        {
            ArrayList sqlList = new ArrayList();//插入sql语句集合
            DataSet ds = GetTreat2(ad_visit_date, ai_visit_no);


            string strTransNo = string.Empty;//输液号
            List<string> transList = new List<string>();//生成输液号保存
            //His00Log.Write("获取病人处置明细相关信息:" + ds.Tables[0].Rows.Count + "条数据!");
            Utility.LogFile.WriteLogAutoError(null,"获取病人处置明细相关信息:" + ds.Tables[0].Rows.Count + "条数据!", this.GetType().Name);
            foreach (DataRow drop in ds.Tables[0].Rows)
            {
                string strPrescDt = drop["就诊日期"].ToString();
                string strItemNo = drop["项目编号"].ToString();
                string strSerialNo = drop["流水号"].ToString();
                // string strOrderNo = drop["ORDER_NO"].ToString();
                if (IsOrderExecHaveData(strSerialNo))//已拆分的不再拆分
                {
                    continue;//结束单次循环
                }
                List<string> listExec = SplitByFrequency(drop, dictFrq);
                int j = 1;
                foreach (string strExec in listExec)
                {
                    string strExecDay = strExec.Split(';')[0].ToString();//预计执行时间
                    string strTotal = strExec.Split(';')[1].ToString(); //总执行次数    
                    string strPerTimes = strExec.Split(';')[2].ToString();//当天执行次数 
                    string strWhichDays = strExec.Split(';')[3].ToString();//第几天执行 Add-1129
                    string sql;

                    int iFlag = 0;

                    //foreach (string s in transList)
                    //{
                    //    if (s.Split(';')[0].ToString() == strPrescDt + strOrderNo + strExecDay + strPerTimes)
                    //    {
                    //        strTransNo = s.Split(';')[1].ToString();
                    //        iFlag = 1;
                    //        break;
                    //    }
                    //}
                    if (iFlag == 0)
                    {   //应佛山顺德慢性病防治中心要求治疗号
                        strTransNo = ws.ExecuteScalarStr("SELECT NUROUTP_EXECUTE_NO.NEXTVAL FROM DUAL");

                        transList.Add(strPrescDt + strExecDay + strPerTimes + ";" + strTransNo);
                    }

                    sql = @"INSERT INTO NUROUTP.NUROUTP_EXECUTE_STANDARD(CLINIC_NO, ORDER_NO, ORDER_SUB_NO, EXECUTE_NO,PERFORM_TIMES,DEPT_CODE,CREATE_TIME,
                           CREATE_NURSE,DOCTOR,ITEM_NO,ITEM_CLASS,ITEM_CODE,ITEM_NAME,
                           FREQUENCY,
                           PATIENT_ID,TOTAL,CURR_COUNT,EXECUTE_STATUS,
                           SCHEDULE_PERFORM_TIME,VISIT_NO,ABIDANCE,DOCTOR_NOTE,WHICH_DAY,CHARGES,SERIAL_NO,VISIT_DATE,CLASS_NAME,PERFORMED_BY,EXECUTE_TYPE) VALUES("
                                 + SQL.SqlConvert(drop["CLINIC_NO"].ToString())
                                 + "," + SQL.SqlConvert(drop["ORDER_NO"].ToString())
                                 + "," + SQL.SqlConvert(drop["ORDER_SUB_NO"].ToString())
                                 + "," + strTransNo +
                                 "," + SQL.SqlConvert(strPerTimes) + 
                                 "," + SQL.SqlConvert(drop["开单科室"].ToString()) +
                                 ",to_date('" + ws.GetSysDate().ToString() + "', 'YYYY-MM-DD HH24:MI:SS')" +
                                 "," + SQL.SqlConvert(SystemParm.LoginUser.USER_NAME)+//His00GlobalVars.userFullName) + 
                    "," + SQL.SqlConvert(drop["开单医生"].ToString()) +
                                 "," + SQL.SqlConvert(drop["项目编号"].ToString()) +
                                 "," + SQL.SqlConvert(drop["ORDER_CLASS"].ToString()) +
                                 "," + SQL.SqlConvert(drop["ORDER_CODE"].ToString()) +
                                 "," + SQL.SqlConvert(drop["项目名称"].ToString()) +
                                 "," + SQL.SqlConvert(drop["频次"].ToString()) +
                                 "," + SQL.SqlConvert(strPaId) +
                                 "," + SQL.SqlConvert(strTotal) +
                                 "," + SQL.SqlConvert(j.ToString()) + //当前次数
                                 ",'0'" + ",to_date('" + strExecDay + "', 'YYYY-MM-DD HH24:MI:SS')" +
                                 "," + SQL.SqlConvert(drop["就诊号"].ToString()) +
                                 "," + SQL.SqlConvert(iDays.ToString()) +
                                 "," + SQL.SqlConvert(drop["医生说明"].ToString()) +
                                 "," + Convert.ToInt32(strWhichDays) +
                                 "," + SQL.SqlConvert(drop["CHARGES"].ToString()) +
                                 "," + SQL.SqlConvert(drop["流水号"].ToString()) +
                                 ",to_date('" + strPrescDt + "', 'YYYY-MM-DD HH24:MI:SS')" +
                                 "," + SQL.SqlConvert(drop["类别"].ToString()) +
                                  "," + SQL.SqlConvert(drop["计划执行科室"].ToString()) +
                                  "," + '2' +
                                 // "," + SQL.SqlConvert(drop["POSITION"].ToString()) +   //应佛山顺德慢性病防治中心要求添加部位数
                                 ")";


                    sqlList.Add(sql);
                    j++;
                }
            }
            try
            {
                if (sqlList.Count > 0)
                {
                    //ws.OpenConn();
                    // ws.SaveDataWithTrans(null,sqlList);
                   int yy =ws.ExecuteSqlArray(sqlList);
                   // ws.CloseConn();
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
                throw;
            }
            
        }

        #endregion


        #region 获得处置列表3

        /// <summary>
        /// 获得处置列表3
        /// </summary>
        /// <param name="pat_id"></param>
        /// <param name="vi_date"></param>
        /// <param name="vi_no"></param>
        /// <returns></returns>
        public DataSet GetTreat3(DateTime ad_visit_date, string ai_visit_no)
        {
            //由于英文字符集数据库as 要加双引号所以拼接字符串
            StringBuilder sqlText = new StringBuilder(512);
            sqlText.AppendLine("select dd.execute_no            as \"治疗编号\",");
            sqlText.AppendLine("dd.class_name            as \"项目类别\",");
            sqlText.AppendLine("dd.class_name            as \"类别\",");
            sqlText.AppendLine("dd.ITEM_NAME            as \"项目名称\",");
            sqlText.AppendLine("NVL(oos.FREQUENCY, dd.FREQUENCY)     as \"频次\",");
            sqlText.AppendLine("NVL(oos.ADMINISTRATION, '')          as \"用法\",");
            sqlText.AppendLine("NVL(oos.DOSAGE, 0)                   as \"用量\",");
            sqlText.AppendLine("dd.charges               as \"金额\",");
            sqlText.AppendLine("dic.dept_name            as \"执行科室\",");
            sqlText.AppendLine("(SELECT dept_name  FROM  dept_dict where dept_dict.dept_code=dd.dept_code) as  \"开单科室\",");
            sqlText.AppendLine("dd.doctor_note           as \"医生说明\",");
            sqlText.AppendLine("dd.perform_times         as \"当天执行次数\",");
            sqlText.AppendLine("dd.curr_count            as \"当前执行次数\",");
            sqlText.AppendLine("dd.total                 as \"执行总次数\",");
            sqlText.AppendLine("dd.schedule_perform_time as \"计划执行时间\",");
            sqlText.AppendLine("dd.which_day             as \"第几天\",");
            sqlText.AppendLine("(CASE dd.EXECUTE_STATUS WHEN '1' THEN  '已执行' WHEN '0' THEN '未执行' WHEN '3' THEN '退费'  ELSE  '取消执行'  END) as \"执行状态\",");
            sqlText.AppendLine("dd.start_nurse         as \"执行护士\",");
            sqlText.AppendLine("dd.serial_no             as \"流水号\",");
            sqlText.AppendLine("dd.start_time          as \"执行时间\",");
            sqlText.AppendLine("dd.ITEM_NO            as \"项目编号\",");
            sqlText.AppendLine("dd.ITEM_NO            as \"项目序号\",");
            sqlText.AppendLine("(CASE NVL(oos.CHARGE_INDICATOR, 1) WHEN 1 THEN '已收费' ELSE '未收费' END) as \"收费标识\",");
            sqlText.AppendLine("dd.PATIENT_ID            as \"患者ID\",");
            sqlText.AppendLine("dd.VISIT_NO            as \"就诊号\",");
            sqlText.AppendLine("dd.VISIT_DATE            as \"就诊时间\",");
            sqlText.AppendLine("dd.abidance              as \"共几天\"");
            string sql = @"

                          from NUROUTP_EXECUTE_STANDARD dd
                          LEFT JOIN OUTP_ORDERS_STANDARD oos
                            ON dd.SERIAL_NO = oos.SERIAL_NO
                           AND dd.ORDER_NO = oos.ORDER_NO
                           AND dd.ORDER_SUB_NO = oos.ORDER_SUB_NO
                           AND dd.VISIT_DATE = oos.VISIT_DATE
                           AND dd.VISIT_NO = oos.VISIT_NO
                          JOIN dept_dict dic ON dd.performed_by = dic.dept_code
                         WHERE    dd.VISIT_DATE  = to_date('" + ad_visit_date.ToString()
                            + "','yyyy-MM-dd HH24:mi:ss')"
                            + "and dd.VISIT_NO = " + ai_visit_no
                            //+ " and dd.performed_by = '"+ GVars.Ward_ID + "'"
                            + "and dd.execute_type = '2'" +
                            " order by dd.EXECUTE_STATUS";

            try
            {
                DataSet ds = ws.GetDataBySql(sqlText.ToString()+ sql, "NUROUTP_TREAT_EXECUTE", false);
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }


        #endregion


        #region 获得病人列表

        /// <summary>
        /// 获得病人列表
        /// </summary>
        /// <param name="performby"></param>
        /// <param name="ldt_start"></param>
        /// <param name="ldt_end"></param>
        /// <returns></returns>
        public DataSet GetPats_All(string performby, DateTime ldt_start, DateTime ldt_end, string dep_except)
        {

            //由于英文字符集数据库as 要加双引号所以拼接字符串
            StringBuilder sqlText = new StringBuilder(512);
            sqlText.AppendLine("SELECT distinct  CLINIC_MASTER.PATIENT_ID as \"病人ID\",");
            sqlText.AppendLine("CLINIC_MASTER.NAME as \"姓名\",");
            sqlText.AppendLine("TO_CHAR(CLINIC_MASTER.VISIT_NO) as \"就诊号\",");
            sqlText.AppendLine("CLINIC_MASTER.SEX as \"性别\",");
            sqlText.AppendLine("CLINIC_MASTER.AGE as \"年龄\",");
            sqlText.AppendLine("CLINIC_MASTER.VISIT_DATE as \"就诊日期\",");
            sqlText.AppendLine("CLINIC_MASTER.CLINIC_LABEL as \"号类\",");
            sqlText.AppendLine("CLINIC_MASTER.IDENTITY as \"病人身份\",");
            sqlText.AppendLine("CLINIC_MASTER.CHARGE_TYPE as \"病人费别\",");
            sqlText.AppendLine("CLINIC_MASTER.REGISTERING_DATE as \"挂号时间\",");
            sqlText.AppendLine("dept_dict.dept_name as \"科室\",");
            sqlText.AppendLine("pat_master_index.DATE_OF_BIRTH,");
            sqlText.AppendLine("pat_master_index.DATE_OF_BIRTH,");
            
            string sql = @"

                               CLINIC_MASTER.CLINIC_NO
                          FROM CLINIC_MASTER, dept_dict, pat_master_index,OUTP_ORDERS_STANDARD 
                         WHERE CLINIC_MASTER.VISIT_DEPT = dept_dict.dept_code
                          
                           AND (CLINIC_MASTER.VISIT_DATE >= to_date('" + ldt_start.ToShortDateString()
                            + " 00:00:00','yyyy-MM-dd HH24:mi:ss')) AND (CLINIC_MASTER.VISIT_DATE <= to_date('" + ldt_end.ToShortDateString()
                            + @" 00:00:00','yyyy-MM-dd HH24:mi:ss')) AND clinic_master.returned_date is null and pat_master_index.PATIENT_ID = CLINIC_MASTER.PATIENT_ID  
                           and CLINIC_MASTER.Patient_Id = OUTP_ORDERS_STANDARD.patient_id 
                           and CLINIC_MASTER.VISIT_NO = OUTP_ORDERS_STANDARD.visit_no
                           AND CLINIC_MASTER.HIS_UNIT_CODE = OUTP_ORDERS_STANDARD.HIS_UNIT_CODE
                           and OUTP_ORDERS_STANDARD.charge_indicator = '1'
                           and OUTP_ORDERS_STANDARD.ORDER_CLASS = 'E'
                           and CLINIC_MASTER.HIS_UNIT_CODE = '" + PlatCommon.SysBase.SystemParm.HisUnitCode + "'";

            if (performby!="*")
            {
                sql += " and OUTP_ORDERS_STANDARD.performed_by = " + SQL.SqlConvert(performby);
            }
            else if (!string.IsNullOrEmpty(dep_except))
            {
                sql += "  and OUTP_ORDERS_STANDARD.performed_by not in(" + dep_except + ")";
            }
            try
            {
                DataSet ds = ws.GetDataBySql(sqlText.ToString()+sql, "CLINIC_MASTER", false);
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }


        #endregion


        #region 获取扫码得到的执行行

        /// <summary>
        /// 获取扫码得到的执行行
        /// </summary>
        /// <param name="exno"></param>
        /// <returns></returns>
        public DataSet GetexNorow(string exno)
        {
            string sql3 = @"select A.PATIENT_ID,A.ITEM_CODE,A.SCHEDULE_PERFORM_TIME,A.SERIAL_NO, A.ITEM_NO ,A.EXECUTE_NO  from NUROUTP.NUROUTP_EXECUTE_STANDARD A WHERE A.EXECUTE_STATUS IN( '0','2')  AND A.EXECUTE_NO ='" + exno + "'";

            DataSet dd = ws.GetDataBySql(sql3, "NUROUTP.NUROUTP_EXECUTE_STANDARD", false);
            return dd;
        }

        #endregion


        #region 根据扫码得到的行获取相同项目相同天得执行数据

        public DataSet GetexRows(string patientId, string exday)
        {
            string sql = @" select * from NUROUTP.NUROUTP_EXECUTE_STANDARD A WHERE A.PATIENT_ID = '" + patientId + "' AND A.SCHEDULE_PERFORM_TIME = to_date('" + exday + "', 'YYYY/MM/DD HH24:MI:SS')";

            DataSet ds = ws.GetDataBySql(sql, "NUROUTP.NUROUTP_EXECUTE_STANDARD", false);

            return ds;
        }


        #endregion


        #region 更新集合

        /// <summary>
        /// 更新集合
        /// </summary>
        /// <param name="arrayList"></param>
        /// <returns></returns>
        public bool UpdateArrayList(ArrayList arrayList)
        {
            try
            {
                ws.ExecuteSqlArray(arrayList);
                return true;
            }
            catch (Exception )
            {
                return false;
            }
        }

        #endregion


        #region 查询检查治疗医嘱执行科室

        /// <summary>
        /// 获取选中的医嘱序号,来查询执行科室
        /// </summary>
        /// <param name="serialNo">序号</param>
        /// <returns></returns>
        public string CheckTreatPerfor(string serialNo)
        {
            string performedBy = string.Empty;
            string sql = "SELECT PERFORMED_BY FROM OUTP_ORDERS_STANDARD WHERE SERIAL_NO =" + SQL.SqlConvert(serialNo)
                + " AND OUTP_ORDERS_STANDARD.HIS_UNIT_CODE = " + SQL.SqlConvert(PlatCommon.SysBase.SystemParm.HisUnitCode); ;
            performedBy = ws.ExecuteScalarStr(sql);
            return performedBy;
        }

        #endregion


        #region 获取护士

        /// <summary>
        /// 获取护士
        /// </summary>
        /// <param name="emp_no"></param>
        /// <returns></returns>
        public DataSet GetNurse(string emp_no)
        {
            string sql = @"select * from staff_dict d  where d.emp_no= " + SQL.SqlConvert(emp_no);

            DataSet dd = ws.GetDataBySql(sql, "staff_dict", false);

            return dd;
        }

        #endregion


        #region 生成执行治疗语句

        /// <summary>
        /// 生成治疗SQL语句
        /// </summary>
        /// <param name="execute_no"></param>
        /// <param name="executenurse"></param>
        /// <param name="execute_nures_id"></param>
        /// <returns></returns>
        public ArrayList ExecuteOrdersList(string execute_no, string executenurse, string execute_nures_id)
        {
            DataSet row1 = GetexNorow(execute_no);
            ArrayList arrayList1 = new ArrayList();
            if (row1.Tables[0].Rows.Count > 0)
            {
                //屏蔽掉执行当天所有的方式，直接单个执行
               // DataSet rows1 = GetexRows(row1.Tables[0].Rows[0]["PATIENT_ID"].ToString(), row1.Tables[0].Rows[0]["SCHEDULE_PERFORM_TIME"].ToString());

                if (row1.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow dr in row1.Tables[0].Rows)
                    {
                                string sql1 = "UPDATE NUROUTP.NUROUTP_EXECUTE_STANDARD SET EXECUTE_STATUS='1',START_TIME=" + SQL.GetOraDate(DateTime.Now) +
                        ",START_NURSE=" + SQL.SqlConvert(executenurse) +
                        ",EXECUTE_NURSE_NO=" + SQL.SqlConvert(execute_nures_id) +
                         " WHERE EXECUTE_NO=" + SQL.SqlConvert(dr["EXECUTE_NO"].ToString());
                                arrayList1.Add(sql1);
                                string sql2 = "UPDATE OUTP_ORDERS_STANDARD SET EXECUTE_STATUS='1',EXECUTE_TIME=" + SQL.GetOraDate(DateTime.Now)
                       + ",EXECUTE_USER_NAME=" + SQL.SqlConvert(execute_nures_id)
                       + " WHERE SERIAL_NO=" + SQL.SqlConvert(dr["SERIAL_NO"].ToString())
                        + " AND ITEM_NO = " + SQL.SqlConvert(dr["ITEM_NO"].ToString());
                                arrayList1.Add(sql2);


                    }

                   
                }
            }
            return arrayList1;
        }
        #endregion


        #region 生成退费语句

        /// <summary>
        /// 生成退费SQL语句
        /// </summary>
        /// <param name="execute_no"></param>
        /// <param name="executenurse"></param>
        /// <param name="execute_nures_id"></param>
        /// <returns></returns>
        public ArrayList RefundOrdersList(string execute_no, string executenurse, string execute_nures_id)
        {
            DataSet row1 = GetexNorow(execute_no);
            ArrayList arrayList1 = new ArrayList();
            if (row1.Tables[0].Rows.Count > 0)
            {
                //屏蔽掉执行当天所有的方式，直接单个执行
                // DataSet rows1 = GetexRows(row1.Tables[0].Rows[0]["PATIENT_ID"].ToString(), row1.Tables[0].Rows[0]["SCHEDULE_PERFORM_TIME"].ToString());

                if (row1.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow dr in row1.Tables[0].Rows)
                    {

                        //退费填写EXECUTE_STATUS='3'，1为已执行2为取消执行3为退费.并且记录END_TIME退费才记录，其他情况不记录,记录END_NURSE和
                        string sql1 = "UPDATE NUROUTP.NUROUTP_EXECUTE_STANDARD SET EXECUTE_STATUS='3',END_NURSE = " + SQL.SqlConvert(executenurse)+ ",EXECUTE_NURSE_NO = "+SQL.SqlConvert(execute_nures_id)+" ,END_TIME= " + SQL.GetOraDate(DateTime.Now) +
                 " WHERE EXECUTE_NO=" + SQL.SqlConvert(dr["EXECUTE_NO"].ToString());
                        arrayList1.Add(sql1);
                        //退费填写EXECUTE_STATUS='3'，1为已执行2为取消执行3为退费，并且记录EXECUTE_TIME及EXECUTE_USER_NAME
                        string sql2 = "UPDATE OUTP_ORDERS_STANDARD SET EXECUTE_STATUS='3',EXECUTE_TIME=" + SQL.GetOraDate(DateTime.Now)
               + ",EXECUTE_USER_NAME=" + SQL.SqlConvert(execute_nures_id)
               + " WHERE SERIAL_NO=" + SQL.SqlConvert(dr["SERIAL_NO"].ToString()) 
               + " AND ITEM_NO = " + SQL.SqlConvert(dr["ITEM_NO"].ToString());
                        arrayList1.Add(sql2);


                    }


                }
            }
            return arrayList1;
        }

        #endregion


        #region 取消执行医嘱

        /// <summary>
        /// 生成取消执行SQL语句
        /// </summary>
        /// <param name="serialNo"></param>
        /// <param name="itemNo"></param>
        /// <param name="executeNo"></param>
        /// <param name="executeNurse"></param>
        /// <param name="executenurseNo"></param>
        /// <returns></returns>
        public ArrayList CancelExecuteOrders(string serialNo,string itemNo,string executeNo,string executeNurse,string executenurseNo)
        {

            ArrayList arrayList = new ArrayList();

            //退费填写EXECUTE_STATUS='3'，1为已执行2为取消执行3为退费，并且记录END_TIME退费才记录，其他情况不记录
            string sql1 = "UPDATE NUROUTP.NUROUTP_EXECUTE_STANDARD SET EXECUTE_STATUS='2',START_NURSE = NULL, START_TIME = NULL,END_NURSE =  " + SQL.SqlConvert(executeNurse)+",EXECUTE_NURSE_NO = "+SQL.SqlConvert(executenurseNo)+",END_TIME= " + SQL.GetOraDate(DateTime.Now) +
     " WHERE EXECUTE_NO=" + SQL.SqlConvert(executeNo);
            arrayList.Add(sql1);
            string sql = "UPDATE OUTP_ORDERS_STANDARD SET EXECUTE_STATUS='2',EXECUTE_TIME=" + SQL.GetOraDate(DateTime.Now)
                + ",EXECUTE_USER_NAME=" + SQL.SqlConvert(executenurseNo)
                + " WHERE SERIAL_NO=" + SQL.SqlConvert(serialNo)
                 + " AND ITEM_NO = " + SQL.SqlConvert(itemNo);

            arrayList.Add(sql);

            return arrayList;
        }

        #endregion


        #region 修改执行时间

        public ArrayList ReviseExecuteTime(string dtExecuteTime,string serailNo)
        {
            ArrayList arrayList = new ArrayList();
            string sql = "UPDATE OUTP_ORDERS_STANDARD SET EXECUTE_TIME= " + SQL.GetOraDate(dtExecuteTime)
                         + " WHERE SERIAL_NO=" + SQL.SqlConvert(serailNo);
            arrayList.Add(sql);

            return arrayList;
        }

        #endregion


        #region 批量执行处置


        /// <summary>
        /// 批量处置执行
        /// </summary>
        /// <param name="row1"></param>
        /// <param name="execute_nures"></param>
        /// <param name="execute_nures_id"></param>
        /// <returns></returns>
        public bool  BachExecute(DataSet row1,string execute_nures, string execute_nures_id)
        {
            bool suc = false;
            if (row1.Tables[0].Rows.Count > 0)
            {      //屏蔽执行当天所有的治疗处置
                //DataSet rows1 = GetexRows(row1.Tables[0].Rows[0]["PATIENT_ID"].ToString(), row1.Tables[0].Rows[0]["SCHEDULE_PERFORM_TIME"].ToString());
                ArrayList arrayList1 = new ArrayList();
                if (row1.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow dr in row1.Tables[0].Rows)
                    {
                        string sql1 = "UPDATE NUROUTP.NUROUTP_EXECUTE_STANDARD SET EXECUTE_STATUS='1',EXECUTE_TIME=" + SQL.GetOraDate(DateTime.Now) +
                ",EXECUTE_NURSE=" + SQL.SqlConvert(execute_nures) +
                ",EXECUTE_NURSE_NO=" + SQL.SqlConvert(execute_nures_id) +
                 " WHERE EXECUTE_NO=" + SQL.SqlConvert(dr["EXECUTE_NO"].ToString());
                        arrayList1.Add(sql1);
                        string sql2 = "UPDATE OUTP_ORDERS_STANDARD SET EXECUTE_STATUS='1',EXECUTE_TIME=" + SQL.GetOraDate(DateTime.Now)
               + ",EXECUTE_USER_NAME=" + SQL.SqlConvert(execute_nures_id)
               + " WHERE SERIAL_NO=" + SQL.SqlConvert(dr["SERIAL_NO"].ToString())
                + " AND ITEM_NO = " + SQL.SqlConvert(dr["ITEM_NO"].ToString());
                        arrayList1.Add(sql2);
                    }
                    suc = UpdateArrayList(arrayList1);

                    return suc;

                }
                return suc;

            }
            else
            {
                return suc;
            }
        }

        #endregion


        #region 获得处置列表2
        /// <summary>
        /// 获得处置列表2
        /// </summary>
        /// <param name="pat_id"></param>
        /// <param name="vi_date"></param>
        /// <param name="vi_no"></param>
        /// <returns></returns>
        public DataSet GetTreat2(DateTime ad_visit_date, string ai_visit_no)
        {

            //由于英文字符集数据库as 要加双引号所以拼接字符串
            StringBuilder sqlText = new StringBuilder(512);

            sqlText.AppendLine("SELECT CLINIC_ITEM_CLASS_DICT.CLASS_NAME as \"类别\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.VISIT_DATE as \"就诊日期\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.ORDER_TEXT as \"项目名称\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.AMOUNT as \"数量\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.UNITS as \"单位\",");
            sqlText.AppendLine(" OUTP_ORDERS_STANDARD.FREQUENCY as \"频次\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.ordered_by as \"开单科室\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.CHARGES as \"实收\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.FREQ_DETAIL as \"医生说明\",");
            sqlText.AppendLine("(CASE OUTP_ORDERS_STANDARD.CHARGE_INDICATOR WHEN 1 THEN '已收费' ELSE '未收费' END) as \"收费标识\",");
            sqlText.AppendLine("(CASE OUTP_ORDERS_STANDARD.EXECUTE_STATUS WHEN '0' THEN '已执行' WHEN '1' THEN '取消执行' ELSE '' END) as \"执行状态\",");
            sqlText.AppendLine("TO_CHAR(OUTP_ORDERS_STANDARD.EXECUTE_TIME,'yyyy-MM-dd HH24:mi') AS \"执行时间\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.EXECUTE_NURSE AS \"执行人\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.SERIAL_NO  AS \"流水号\",");
            sqlText.AppendLine(" OUTP_ORDERS_STANDARD.ORDER_NO AS \"项目编号\",");
            sqlText.AppendLine(" OUTP_ORDERS_STANDARD.ORDER_CODE,");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.COSTS,");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.VISIT_NO \"就诊号\",");
            sqlText.AppendLine(" '0' del_indicator,");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.ITEM_SPEC,");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.APPOINT_NO,");
            sqlText.AppendLine("CLINIC_MASTER.DOCTOR AS \"开单医生\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.ORDER_CLASS,");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.FREQUENCY,");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.CHARGES,");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.performed_by as \"计划执行科室\",");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.CLINIC_NO,");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.ORDER_NO,");
            sqlText.AppendLine("OUTP_ORDERS_STANDARD.ORDER_SUB_NO");
            string sql = @"
                          FROM OUTP_ORDERS_STANDARD,
                               CLINIC_MASTER,
                               dept_dict,
                               CLINIC_ITEM_CLASS_DICT
      
                         WHERE (OUTP_ORDERS_STANDARD.VISIT_NO = CLINIC_MASTER.VISIT_NO)
                           and (OUTP_ORDERS_STANDARD.VISIT_DATE = CLINIC_MASTER.VISIT_DATE)
                           and (OUTP_ORDERS_STANDARD.HIS_UNIT_CODE = CLINIC_MASTER.HIS_UNIT_CODE)
                           and (OUTP_ORDERS_STANDARD.ORDER_CLASS =
                               CLINIC_ITEM_CLASS_DICT.CLASS_CODE)
                           and OUTP_ORDERS_STANDARD.PERFORMED_BY = dept_dict.dept_code
                           and CLINIC_ITEM_CLASS_DICT.CLASS_CODE IN ('E', 'F', 'C', 'D')
                           and OUTP_ORDERS_STANDARD.CHARGE_INDICATOR = '1'
                           and CLINIC_MASTER.VISIT_DATE  = to_date('" + ad_visit_date.ToString()
                            + "','yyyy-MM-dd HH24:mi:ss')"
                            + " and OUTP_ORDERS_STANDARD.visit_no = '" + ai_visit_no + "'"
                            + " and CLINIC_MASTER.HIS_UNIT_CODE = " + SQL.SqlConvert(PlatCommon.SysBase.SystemParm.HisUnitCode) ;

            try
            {
                DataSet ds = ws.GetDataBySql(sqlText.ToString()+sql, "OUTP_ORDERS_STANDARD", false);
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        #endregion


        #region  检查处方明细是否已经拆分到输液执行表

        /// <summary>
        /// 检查处方明细是否已经拆分到输液执行表
        /// </summary>
        /// <param name="strPaId">病人ID</param>
        /// <param name="strPrescNo">处方号</param>
        /// <param name="strSerialNo">流水号</param>
        /// <param name="strItemNo">项目序号</param>
        /// <returns></returns>
        private bool IsOrderExecHaveData(string strSerialNo)
        {
            string sql = "SELECT * FROM NUROUTP_EXECUTE_STANDARD WHERE SERIAL_NO = " + SQL.SqlConvert(strSerialNo);
            DataSet ds = ws.GetDataBySql(sql, "NUROUTP_TREAT_EXECUTE", false);
            if (ds.Tables[0].Rows.Count < 1)
            {
                return false;
            }
            return true;
        }

        #endregion


        #region  拆分返回结果集


        /// <summary>
        /// 拆分返回结果集
        /// </summary>
        /// <param name="drop"></param>
        /// <param name="dicFreq"></param>
        /// <returns></returns>
        private List<string> SplitByFrequency(DataRow drop, Dictionary<string, string> dicFreq)
        {
            List<string> list1 = new List<string>();//拆分后返回结果集
           
            // int iDays = Int32.Parse(drop["ABIDANCE"].ToString());//用药天数
            string strFreqOrg = drop["FREQUENCY"].ToString();//频次

            string strFreq = TranslateFreq(strFreqOrg, dicFreq); //翻译频次
            #region 判断处理频次定义里是不是第2种模式（包括次，如：1次/天）,第1种模式如 1/天，内蒙古肿瘤医院使用第2种模式 20171212

            if (strFreq.Contains("次"))
            {
                strFreq = strFreq.Replace("次", "");
            }

            #endregion
           
            string markers = "1";//查看是多部位执行还是单部位执行
            string strTimes = drop["数量"].ToString();
            string hhh= strFreq.Split('/')[0].ToString();//
            string jjjj= strFreq.Split('/')[1].ToString();//
            if (strTimes.Length==0)
            {
                strTimes = "1";
            }
            iDays = CountDays(Int32.Parse(strTimes),jjjj,Int32.Parse(hhh));
            //iDays = drop["ABIDANCE"].ToInt();
            int iPerTimes = 0;
            if (markers.Equals("1"))
            {
                iPerTimes = Int32.Parse(strTimes);//执行次数
            }
            else
            {
                iPerTimes = GetExecuteCount2(drop);//计算执行次数
            }
            if (strFreq.Contains("即刻") || strFreq.Contains("必要时") || strFreq.Contains("术晨") || strFreq.Contains("术前") || strFreq.Contains("术前30") || strFreq.Contains("基础量") || strFreq.Contains("大剂量"))
            {
                //频次为特别定义的（如：即刻 必要时 术晨 术前 术前30’等）
                list1.Add(DateTime.Now.Date.ToString() + ";" + 1 + ";" + "1" + ";" + "1");
                return list1;
            }
            else if (strFreq.IndexOf("/") < 0)//正常频次的
            {
                System.Windows.Forms.MessageBox.Show("用药频次异常，拆分失败，请检查处方!", "系统提示");

                return list1;
            }


            int iIntervalTimes = Convert.ToInt32(strFreq.Split('/')[0].ToString());//每间隔时间的次数
            //int iIntervalTimes = Convert.ToInt32(strInterval);//每间隔时间的次数
            string strFreqInterval = strFreq.Split('/')[1].ToString();//间隔时间

            double dHour = 1;
            if (strFreqInterval.Contains("小时"))
            {
                string strH = strFreqInterval.Substring(0, strFreqInterval.IndexOf("小时"));
                if (!string.IsNullOrEmpty(strH))
                {
                    dHour = Convert.ToDouble(strH);
                }
                strFreqInterval = "小时";
            }

            double dWeek = 1;
            if (strFreqInterval.Contains("周"))
            {
                string strW = strFreqInterval.Substring(0, strFreqInterval.IndexOf("周"));
                if (!string.IsNullOrEmpty(strW))
                {
                    dWeek = Convert.ToDouble(strW);
                }
                strFreqInterval = "周";
            }

            int iWhichDay = 1;//第几天执行
            switch (strFreqInterval)
            {
                case "日":
                    int k = 1;
                    int iTimeRi = Convert.ToInt32(strFreq.Split('/')[0].ToString());
                    for (int i = 0; i < iDays; i++)
                    {
                        string strTime = DateTime.Now.Date.AddDays(i).ToString();
                        for (int j = 0; j < iTimeRi; j++)
                        {
                            //计划时间;总执行次数;当天执行次数;第几天执行
                            list1.Add(strTime + ";" + iPerTimes + ";" + (j + 1).ToString() + ";" + iWhichDay);
                            k++;
                            if (k > iPerTimes)
                            {
                                break;
                            }
                        }
                        iWhichDay++;
                    }
                    break;
                case "小时":
                    List<string> listHour = new List<string>();
                    List<DateTime> listDt = new List<DateTime>();
                    if (iIntervalTimes > 1)
                    {
                        for (int i = 0; i < iPerTimes; i++)
                        {
                            DateTime strTime = DateTime.Now.AddMinutes(dHour * 60 * i / iIntervalTimes);
                            if (!listDt.Contains(strTime.Date))
                            {
                                listDt.Add(strTime.Date);
                            }
                            listHour.Add(strTime.Date.ToString() + ";" + iPerTimes + "|");
                        }
                    }
                    else
                    {
                        for (int i = 0; i < iPerTimes; i++)
                        {
                            DateTime strTime = DateTime.Now.AddHours(dHour * i);
                            if (!listDt.Contains(strTime.Date))
                            {
                                listDt.Add(strTime.Date);
                            }
                            listHour.Add(strTime.Date.ToString() + ";" + iPerTimes + "|");//执行时间只记录几月几日
                        }
                    }
                    //int iWhichDay = 1;
                    foreach (DateTime d in listDt)
                    {
                        int iCnt = 1;
                        foreach (string s in listHour)
                        {
                            if (d == Convert.ToDateTime(s.Split(';')[0]).Date)
                            {
                                string strNew = s.Insert(s.IndexOf('|') + 1, iCnt.ToString());
                                list1.Add(strNew.Replace('|', ';') + ";" + iWhichDay);
                                iCnt++;
                            }
                        }
                        iWhichDay++;
                    }
                    break;
                case "周":
                    if (dWeek > 1)
                    {
                        for (int i = 0; i < iPerTimes; i++)
                        {
                            DateTime dtW = DateTime.Now.AddDays(7 * dWeek * i);
                            list1.Add(dtW.Date.ToString() + ";" + iPerTimes + ";" + "1" + ";" + iWhichDay);
                            iWhichDay++;
                        }
                        break;
                    }
                    string strNote = drop["DOCTOR_NOTE"].ToString().Replace('，', ',');//医生说明："1,2,3,4,5,6,7"表示星期一,二,三,...日
                    if (string.IsNullOrEmpty(strNote))
                    {
                        System.Windows.Forms.MessageBox.Show("按周执行需写明医生用药说明!", "系统提示");
                    }
                    int iTimes = Convert.ToInt32(strFreq.Split('/')[0]);//一周几次(1/周 2/周 3/周 4/周 5/周 6/周)
                    int iWeek = iPerTimes / iTimes + 1;//周数
                    DateTime dtLast = DateTime.Now.AddDays(7 * iWeek);
                    List<DateTime> dtList = new List<DateTime>();
                    DateTime dt1 = DateTime.Now;
                    int n = 0;
                    while (dt1 < dtLast)
                    {
                        dt1 = DateTime.Now.AddDays(n);
                        n++;
                        dtList.Add(dt1);//获取这几周内的所有日期集合
                    }
                    foreach (DateTime dExec in dtList)
                    {
                        int dayOfWeek = (int)dExec.DayOfWeek;
                        dayOfWeek = (dayOfWeek == 0 ? 7 : dayOfWeek);
                        if (strNote.Contains(dayOfWeek.ToString()))//根据医生说明判断哪些日期需要执行
                        {
                            list1.Add(dExec.Date.ToString() + ";" + iPerTimes + ";" + "1" + ";" + iWhichDay);
                            iWhichDay++;
                        }
                        if (list1.Count == iPerTimes)//根据执行次数判断结束
                        {
                            break;
                        }
                    }
                    break;
                case "隔日":  //（1/隔日）
                    for (int i = 0; i < iPerTimes; i++)
                    {
                        DateTime dtGeRi = DateTime.Now.AddDays(i * 2);
                        list1.Add(dtGeRi.Date.ToString() + ";" + iPerTimes + ";" + "1" + ";" + iWhichDay);
                        iWhichDay++;
                    }
                    break;
                case "月":
                    if (iIntervalTimes > 1)//（n/月）
                    {
                        for (int i = 0; i < iPerTimes; i++)
                        {
                            DateTime dtDt = DateTime.Now.AddDays(30 / iIntervalTimes * i);
                            list1.Add(dtDt.Date.ToString() + ";" + iPerTimes + ";" + "1" + ";" + iWhichDay);
                            iWhichDay++;
                        }
                    }
                    else
                    {
                        for (int i = 0; i < iPerTimes; i++)
                        {
                            DateTime dtMonth = DateTime.Now.AddMonths(i);//（1/月）
                            list1.Add(dtMonth.Date.ToString() + ";" + iPerTimes + ";" + "1" + ";" + iWhichDay);
                            iWhichDay++;
                        }
                    }
                    break;
                case "早":
                case "午":
                case "晚":
                    for (int i = 0; i < iPerTimes; i++)
                    {
                        DateTime dtZao = DateTime.Now.AddDays(i);
                        list1.Add(dtZao.Date.ToString() + ";" + iPerTimes + ";" + "1" + ";" + iWhichDay);
                        iWhichDay++;
                    }
                    break;
                case "半年":
                    for (int i = 0; i < iPerTimes; i++)
                    {
                        DateTime dtMonth = DateTime.Now.AddMonths(i * 6);//（1/半年）
                        list1.Add(dtMonth.Date.ToString() + ";" + iPerTimes + ";" + "1" + ";" + iWhichDay);
                        iWhichDay++;
                    }
                    break;
                default:
                    list1.Add(DateTime.Now.Date.ToString() + ";" + 1 + ";" + "1" + ";" + "1");
                    break;
            }
            return list1;
        }


        #endregion


        #region 应佛山顺德慢性病防治中心要求多个部位计算拆分次数

        /// <summary>
        /// 应佛山顺德慢性病防治中心要求多个部位计算拆分次数
        /// </summary>
        /// <param name="dr1"></param>
        /// <returns></returns>
        private int GetExecuteCount2(DataRow dr1)
        {
            double dAmount = Convert.ToDouble(Convert.ToInt32(dr1["数量"].ToString()).ToString());

            double dDosage = Convert.ToDouble(Convert.ToInt32(dr1["POSITION"].ToString()).ToString());//次用量


            if (dAmount % dDosage == 0)
            {
                return Convert.ToInt32(dAmount / dDosage);
            }
            else
            {
                return 0;
            }


        }

        #endregion


        #region  频次转换

        /// <summary>
        /// 频次转换
        /// </summary>
        /// <param name="strFrom">传入的频次</param>
        /// <param name="dicFreq">频次对照字典</param>
        /// <returns>转换后的频次</returns>
        private string TranslateFreq(string strFrom, Dictionary<string, string> dicFreq)
        {
            foreach (KeyValuePair<string, string> dic in dicFreq)
            {
                if (dic.Key.Contains(strFrom))
                {
                    return dic.Key;
                }
                else
                {
                    if (dic.Value.Contains(strFrom))
                    {
                        return dic.Key;
                    }
                }
            }
            return "";
        }


        #endregion


        #region 计算总天数

        /// <summary>
        /// 计算总天数
        /// </summary>
        /// <param name="count"></param>
        /// <param name="name"></param>
        /// <param name="freq"></param>
        /// <returns></returns>

        public int CountDays(int count,string name,int freq)
        {
            if (name.Contains("日"))
            {
                int countdays = count / freq;
                return countdays;
            }
            else if (name.Contains("小时"))
            {
                int countdays = count / (24 / freq);

                return countdays;
            }
            else
            {
                return count;
            }
           
        }
        #endregion


        #region 查询处置信息


        /// <summary>
        /// 查询处置信息
        /// </summary>
        /// <param name="executeno"></param>
        /// <returns></returns>
        public DataSet ExecuteCount(string executeno)
        {

            string sql = "select execute_status from  NUROUTP_EXECUTE_STANDARD WHERE EXECUTE_NO = " + SQL.SqlConvert(executeno); 

            DataSet eds= ws.GetDataBySql(sql, "NUROUTP_EXECUTE_STANDARD", false);

            return eds;
        }


        #endregion



        #region 获得收费明细记录

        /// <summary>
        /// 获得收费明细记录
        /// </summary>
        /// <param name="visit_date">就诊日期</param>
        /// <param name="visit_no">就诊序号</param>
        /// <param name="patient_id">病人ID</param>
        /// <param name="serial_no">项目流水号</param>
        ///  <param name="item_no">项目编号</param>
        /// <returns>TREAT_REC 表里的item_no 对应orders_costs表里的 order_no</returns>
        /// 
        public DataSet GetOrdersCosts(string visit_date, string visit_no, string patient_id,string serial_no,string item_no)
        {
            string sql = @"SELECT * FROM outp_orders_costs_standard 
                            WHERE patient_id=" + SQL.SqlConvert(patient_id)
                            + " AND VISIT_DATE=to_date('" + visit_date
                            + "','yyyy-MM-dd HH24:mi:ss')"
                            + " AND VISIT_NO=" + visit_no
                            + " AND SERIAL_NO = " +SQL.SqlConvert(serial_no) 
                            + " AND  ORDER_NO= " +SQL.SqlConvert(item_no)
                            + " AND outp_orders_costs_standard.HIS_UNIT_CODE = " + SQL.SqlConvert(PlatCommon.SysBase.SystemParm.HisUnitCode); ;
            try
            {
                DataSet ds = ws.GetDataBySql(sql, "outp_orders_costs", false);
                return ds;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.ToString() + "|SQL:" + sql.ToString());
            }
        }
        #endregion
    }
}
