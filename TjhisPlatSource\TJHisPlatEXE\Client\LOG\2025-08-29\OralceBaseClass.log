2025-08-29 18:49:11
获取数据集失败，SQL:select dd.execute_no            as "治疗编号",
dd.class_name            as "项目类别",
dd.class_name            as "类别",
dd.ITEM_NAME            as "项目名称",
NVL(oos.FREQUENCY, dd.FREQUENCY)     as "频次",
NVL(oos.ADMINISTRATION, '')          as "用法",
NVL(oos.DOSAGE, 0)                   as "用量",
dd.charges               as "金额",
dic.dept_name            as "执行科室",
(SELECT dept_name  FROM  dept_dict where dept_dict.dept_code=dd.dept_code) as  "开单科室",
dd.doctor_note           as "医生说明",
dd.perform_times         as "当天执行次数",
dd.curr_count            as "当前执行次数",
dd.total                 as "执行总次数",
dd.schedule_perform_time as "计划执行时间",
dd.which_day             as "第几天",
(CASE dd.EXECUTE_STATUS WHEN '1' THEN  '已执行' WHEN '0' THEN '未执行' WHEN '3' THEN '退费'  ELSE  '取消执行'  END) as "执行状态",
dd.start_nurse         as "执行护士",
dd.serial_no             as "流水号",
dd.start_time          as "执行时间",
dd.ITEM_NO            as "项目编号",
dd.ITEM_NO            as "项目序号",
(CASE NVL(oos.CHARGE_INDICATOR, '1') WHEN '1' THEN '已收费' ELSE '未收费' END) as "收费标识",
dd.PATIENT_ID            as "患者ID",
dd.VISIT_NO            as "就诊号",
dd.VISIT_DATE            as "就诊时间",
dd.abidance              as "共几天"


                          from NUROUTP_EXECUTE_STANDARD dd
                          LEFT JOIN OUTP_ORDERS_STANDARD oos
                            ON dd.SERIAL_NO = oos.SERIAL_NO
                           AND dd.ITEM_NO = oos.ITEM_NO
                           AND dd.VISIT_DATE = oos.VISIT_DATE
                           AND dd.VISIT_NO = oos.VISIT_NO
                          JOIN dept_dict dic ON dd.performed_by = dic.dept_code
                         WHERE    dd.VISIT_DATE  = to_date('2025/8/29 0:00:00','yyyy-MM-dd HH24:mi:ss')and dd.VISIT_NO = '11016321'and dd.execute_type = '2' order by dd.EXECUTE_STATUSORA-00932: 数据类型不一致: 应为 NUMBER, 但却获得 CHAR
-----------------------------------
2025-08-29 18:53:24
获取数据集失败，SQL:select dd.execute_no            as "治疗编号",
dd.class_name            as "项目类别",
dd.class_name            as "类别",
dd.ITEM_NAME            as "项目名称",
NVL(oos.FREQUENCY, dd.FREQUENCY)     as "频次",
NVL(oos.ADMINISTRATION, '')          as "用法",
NVL(oos.DOSAGE, 0)                   as "用量",
dd.charges               as "金额",
dic.dept_name            as "执行科室",
(SELECT dept_name  FROM  dept_dict where dept_dict.dept_code=dd.dept_code) as  "开单科室",
dd.doctor_note           as "医生说明",
dd.perform_times         as "当天执行次数",
dd.curr_count            as "当前执行次数",
dd.total                 as "执行总次数",
dd.schedule_perform_time as "计划执行时间",
dd.which_day             as "第几天",
(CASE dd.EXECUTE_STATUS WHEN '1' THEN  '已执行' WHEN '0' THEN '未执行' WHEN '3' THEN '退费'  ELSE  '取消执行'  END) as "执行状态",
dd.start_nurse         as "执行护士",
dd.serial_no             as "流水号",
dd.start_time          as "执行时间",
dd.ITEM_NO            as "项目编号",
dd.ITEM_NO            as "项目序号",
(CASE NVL(oos.CHARGE_INDICATOR, '1') WHEN '1' THEN '已收费' ELSE '未收费' END) as "收费标识",
dd.PATIENT_ID            as "患者ID",
dd.VISIT_NO            as "就诊号",
dd.VISIT_DATE            as "就诊时间",
dd.abidance              as "共几天"


                          from NUROUTP_EXECUTE_STANDARD dd
                          LEFT JOIN OUTP_ORDERS_STANDARD oos
                            ON dd.SERIAL_NO = oos.SERIAL_NO
                           AND dd.ORDER_NO = oos.ORDER_NO
                           AND dd.ORDER_SUB_NO = oos.ORDER_SUB_NO
                           AND dd.VISIT_DATE = oos.VISIT_DATE
                           AND dd.VISIT_NO = oos.VISIT_NO
                          JOIN dept_dict dic ON dd.performed_by = dic.dept_code
                         WHERE    dd.VISIT_DATE  = to_date('2025/8/29 0:00:00','yyyy-MM-dd HH24:mi:ss')and dd.VISIT_NO = '11016321'and dd.execute_type = '2' order by dd.EXECUTE_STATUSORA-00932: 数据类型不一致: 应为 NUMBER, 但却获得 CHAR
-----------------------------------
