﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;
using DevExpress.XtraEditors;
//using TJHIS.Base;
//using Tjhis.HisComm;
//using MsgHelper = Tjhis.CsComm.Cs02MessageBox;
//using His00Log = Tjhis.HisComm.His00Log;
using System.Collections;
using System.IO;
using Tjhis.NurOutp.Station;
using Tjhis.NurOutp.Station;
using DevExpress.XtraReports.Parameters;
using SQL = PlatCommon.Base02.Cs02StringHelper;
using NM_Service.NMService;
using PlatCommon.SysBase;
using PlatCommon.Common;
using NM_Service.NMService;
using Tjhis.Interface.Station;
using Tjhis.Interface.Station.IdentityCard;

namespace Tjhis.NurOutp.Station
{

    public partial class frmTreatExecuteStandard : ParentForm
    {
        private ServerPublicClient ws = new NM_Service.NMService.ServerPublicClient();
        srvTreatExecuteStandard ste = new srvTreatExecuteStandard();
        //private ExcelAccess excelAccess = new ExcelAccess();
        DataSet dsPats;
        //DataSet dsDiagnosis;
        //DataSet dsPresc;
        DataSet dsTreat;
        DateTime sdate;       //开始时间
        DateTime edate;       //结束时间
        String execute_nures_id = string.Empty;
        private Dictionary<string, string> FreqVSDict = new Dictionary<string, string>();//频次对照字典
        int days = -1;
        private const string _PatientTemplate = "治疗处置单";
        Dictionary<string, string> dic = new Dictionary<string, string>();

        public frmTreatExecuteStandard()
        {
            InitializeComponent();
        }

        private void frmPatsInfo_Load(object sender, EventArgs e)
        {
            DateTime dtSys = ws.GetSysDate();
            DateTime endDate = new DateTime(dtSys.Year, dtSys.Month, dtSys.Day, 23, 59, 59);
            barDateEdit2.EditValue = endDate;
            DateTime startDate = new DateTime(dtSys.AddDays(days).Year, dtSys.AddDays(days).Month, dtSys.AddDays(days).Day, 0, 0, 0);
            barDateEdit1.EditValue = startDate;
            barStaticItem7.Caption = SystemParm.LoginUser.NAME;  //His00GlobalVars.userFullName;
            execute_nures_id = SystemParm.LoginUser.EMP_NO;
            initPatsInfo();

            barCheckItem1.Checked = true;
        }

        private void xgrvPats_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            DataRowView drv = xgrvPats.GetFocusedRow() as DataRowView;

            string pat_id = drv.Row["病人ID"].ToString();
            DateTime vi_date = DateTime.Parse(drv.Row["就诊日期"].ToString());
            string vi_no = drv.Row["就诊号"].ToString();

            lblID.Text = pat_id;
            lblName.Text = drv.Row["姓名"].ToString();
            lblSex.Text = drv.Row["性别"].ToString();
            lblBirthday.Text = drv.Row["年龄"].ToString();
            label6.Text = drv.Row["病人费别"].ToString();
            DateTime dtTmp;
            string str = "";
            bool b = DateTime.TryParse(drv.Row["DATE_OF_BIRTH"].ToString(), out dtTmp);
            if (dtTmp != null)
            {
                str = dtTmp.ToString("D");
                if (str.Contains("星期"))
                {
                    int pos = str.IndexOf("星期");
                    str = str.ToString().Substring(0, pos);
                }
            }
            lblBirthday.Text = str;
            lblIdentity.Text = drv.Row["病人身份"].ToString();
            //dsDiagnosis = ste.GetDiagnosis(pat_id, vi_date, vi_no);

            //xgrdDiagnosis.DataSource = dsDiagnosis.Tables[0];
            //xgrvDiagnosis.Columns["ORDINAL"].Visible = false;
            //xgrvDiagnosis.Columns["VISIT_DATE"].Visible = false;
            //xgrvDiagnosis.Columns["VISIT_NO"].Visible = false;
            //xgrvDiagnosis.Columns["诊断"].Width = 600;

            //dsPresc = ste.GetPresc(vi_date, vi_no);
            //xgrdPresc.DataSource = dsPresc.Tables[0];

            //xgrvPresc.Columns["药名"].Width = 150;
            //xgrvPresc.Columns["数量"].Width = 40;
            //xgrvPresc.Columns["单位"].Width = 40;
            //xgrvPresc.Columns["用量"].Width = 40;
            //xgrvPresc.Columns["剂数"].Width = 40;

            //xgrvPresc.Columns["DEL_INDICATOR"].Visible = false;
            //xgrvPresc.Columns["TOXI_PROPERTY"].Visible = false;
            //xgrvPresc.Columns["OFFICIAL_CATALOG"].Visible = false;
            //xgrvPresc.Columns["VISIT_NO"].Visible = false;
            //xgrvPresc.Columns["SERIAL_NO"].Visible = false;
            //xgrvPresc.Columns["ITEM_NO"].Visible = false;
            //xgrvPresc.Columns["ITEM_CLASS"].Visible = false;
            //xgrvPresc.Columns["DRUG_CODE"].Visible = false;
            //xgrvPresc.Columns["PROVIDED_INDICATOR"].Visible = false;
            //xgrvPresc.Columns["COSTS"].Visible = false;
            //xgrvPresc.Columns["ORDER_NO"].Visible = false;
            //xgrvPresc.Columns["SUB_ORDER_NO"].Visible = false;
            //xgrvPresc.Columns["SPLIT_FLAG"].Visible = false;
            //xgrvPresc.Columns["PERFORM_TIMES"].Visible = false;



            DateTime.TryParse(barDateEdit1.EditValue.ToString(), out sdate);
            DateTime.TryParse(barDateEdit2.EditValue.ToString(), out edate);
            
            
            ste.splitIntoTransOrderExecute2(pat_id, vi_no, sdate, edate, FreqVSDict, vi_date);
            dsTreat = ste.GetTreat3(vi_date, vi_no);
            xgrdTreat.DataSource = dsTreat.Tables[0];


            xgrvTreat.Columns["执行时间"].Width = 120;
            xgrvTreat.Columns["执行时间"].DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            xgrvTreat.Columns["执行时间"].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            xgrvTreat.Columns["项目名称"].Width = 300;

            // 新增频次、用法、用量列设置
            xgrvTreat.Columns["频次"].Width = 80;
            xgrvTreat.Columns["用法"].Width = 100;
            xgrvTreat.Columns["用量"].Width = 80;

            xgrvTreat.Columns["金额"].Width = 120;
         //   xgrvTreat.Columns["金额"].AppearanceCell.TextOptions.HAlignment=DevExpress.;
            xgrvTreat.Columns["开单科室"].Width = 120;
            xgrvTreat.Columns["执行科室"].Width = 120;
            xgrvTreat.Columns["医生说明"].Visible = false;
            xgrvTreat.Columns["当天执行次数"].Visible = false;
            xgrvTreat.Columns["当前执行次数"].Visible = false;
            xgrvTreat.Columns["执行总次数"].Visible = false; 
            xgrvTreat.Columns["计划执行时间"].Visible = false;
            xgrvTreat.Columns["第几天"].Visible = false;
            //xgrvTreat.Columns["执行状态"].Visible = false;
            xgrvTreat.Columns["执行护士"].Visible = false;
            xgrvTreat.Columns["流水号"].Visible = false;
            xgrvTreat.Columns["执行时间"].Visible = false;
            xgrvTreat.Columns["项目编号"].Visible = false;
            xgrvTreat.Columns["患者ID"].Visible = false;
            xgrvTreat.Columns["就诊号"].Visible = false;
            xgrvTreat.Columns["就诊时间"].Visible = false;
            xgrvTreat.Columns["共几天"].Visible = false;


            //xgrdTreat.Columns["SERIAL_NO"].Visible = false;
            xgrvTreat_FocusedRowChanged(null, null);

        }

        private void textEdit_EditValueChanged(object sender, EventArgs e)
        {
            try
            {

                if (dsPats == null) return;//空数据就返回 yanjing 2018-6-25
                if (textEdit.Text.Length > 0)
                {
                    DataTable searchDt = dsPats.Tables[0].Copy();
                    string signID = rG_ID.Properties.Items[rG_ID.SelectedIndex].Value.ToString();
                    switch (signID)
                    {
                        case "病人ID":
                            searchDt.DefaultView.RowFilter = " 病人ID like '%" + textEdit.Text + "%'";
                            break;
                        case "就诊号":
                            searchDt.DefaultView.RowFilter = " 就诊号 like '%" + textEdit.Text + "%'";
                            break;
                    }

                    xgrdPats.DataSource = searchDt.DefaultView.ToTable();

                    if (textEdit.EditValue != null && textEdit.EditValue.ToString().Length == 10)
                    {
                        textEdit.EditValue = null;
                    }

                }
                else
                {
                    xgrdPats.DataSource = dsPats.Tables[0];
                }



            }

            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }



        }


        private void rG_ID_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (dsPats == null) return;//空数据就返回 yanjing 2018-6-25
            if (textEdit.Text.Length > 0)
            {
                DataTable searchDt = dsPats.Tables[0].Copy();
                string signID = rG_ID.Properties.Items[rG_ID.SelectedIndex].Value.ToString();
                switch (signID)
                {
                    case "病人ID":
                        searchDt.DefaultView.RowFilter = " 病人ID like '%" + textEdit.Text + "%'";
                        break;
                    case "就诊号":
                        searchDt.DefaultView.RowFilter = " 就诊号 like '%" + textEdit.Text + "%'";
                        break;
                }
                xgrdPats.DataSource = searchDt.DefaultView.ToTable();
            }
            else
                xgrdPats.DataSource = dsPats.Tables[0];
        }

        private void barClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barDateEdit1_EditValueChanged(object sender, EventArgs e)
        {
            //initPatsInfo();
        }

        private void barDateEdit2_EditValueChanged(object sender, EventArgs e)
        {
            //initPatsInfo();
        }
        #region 初如化病人信息
        private void initPatsInfo()
        {
            //读取配置好的频次对照
            string iniFile1 = System.IO.Path.Combine(Application.StartupPath, "频次对照.ini");
            if (System.IO.File.Exists(iniFile1) == true)
            {
                string FREQ_LIST = GVars.IniFile.ReadStringByFile("FREQ_DIC", "FREQ_DIC", string.Empty, iniFile1);//频次
                string[] freqs = FREQ_LIST.Split(';');
                foreach (string strKey in freqs)
                {
                    string strFreqVal = GVars.IniFile.ReadStringByFile("FREQ_VS", strKey, string.Empty, iniFile1);
                    FreqVSDict.Add(strKey, strFreqVal);
                }
            }
            //dsPats = ste.GetPats_All(His00GlobalVars.deptCode, (DateTime)barDateEdit1.EditValue, (DateTime)barDateEdit2.EditValue);

            if (GVars.DEP_POWER)
            {
                dsPats = ste.GetPats_All("*", (DateTime)barDateEdit1.EditValue, (DateTime)barDateEdit2.EditValue,GVars.DEP_EXCEPT);
            }
            else
            {
               // string deptid = GVars.Ward_ID.Substring(0,4);

                dsPats = ste.GetPats_All(GVars.Ward_ID, (DateTime)barDateEdit1.EditValue, (DateTime)barDateEdit2.EditValue, GVars.DEP_EXCEPT);
            }
            
            xgrdPats.DataSource = dsPats.Tables[0];
            xgrvPats.Columns["CLINIC_NO"].Visible = false;
            xgrvPats.Columns["DATE_OF_BIRTH"].Visible = false;
            xgrvPats.Columns["性别"].Width = 40;
            xgrvPats.Columns["年龄"].Width = 40;
            if (dsPats.Tables[0].Rows.Count > 0)
            {
                xgrvPats.SelectRow(0);
            }

            
        }
        #endregion

        private void barReader_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //this.Cursor = Cursors.WaitCursor;
            //IntPtr handleTmp = new IntPtr();
            //RFIDCardReader rf = new RFIDCardReader();
            //int stTmp = RFIDCard.mwDevOpen("USB", "", out handleTmp);
            //if (stTmp > 0)
            //{
            //    //RFIDCardReader.Init();
            //    rf.Beep();
            //    barCardNo.EditValue = rf.ReadPaCardNo();
            //    rf.Close();//读完关闭
            //    DataRow[] drs = dsPats.Tables[0].Select("卡号 = '" + barCardNo.EditValue.ToString() + "'");
            //    if (drs.Length > 0)
            //    {
            //        int location = dsPats.Tables[0].Rows.IndexOf(drs[0]);
            //        xgrvPats.SelectRow(location);
            //    }
            //}
            //else
            //{
            //    XtraMessageBox.Show("设备打开失败！", "错误信息", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //}
            //this.Cursor = Cursors.Default;
        }

        private void repositoryItemTextEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            //try
            //{
            //    if (e.KeyCode == Keys.Enter)
            //    {
            //        if (((DevExpress.XtraEditors.TextEdit)sender).Text != "")
            //        {
            //            DataRow[] drs = dsPats.Tables[0].Select("卡号 = '" + ((DevExpress.XtraEditors.TextEdit)sender).Text + "'");
            //            if (drs.Length > 0)
            //            {
            //                int location = dsPats.Tables[0].Rows.IndexOf(drs[0]);
            //                xgrvPats.ClearSelection();
            //                xgrvPats.FocusedRowHandle = location;
            //                xgrvPats.SelectRow(location);
            //            }
            //        }
            //    }
            //}
            //catch (Exception ex)
            //{
            //    His00Log.Write(ex.ToString());
            //}
        }

        #region 已开处置信息鼠标触发
        private void xgrdTreat_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                popupMenu1.ShowPopup(Control.MousePosition);
            }
        }
        #endregion

        #region 执行医嘱
        private void barExecute_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (xgrvTreat.RowCount <= 0) return;
                DataRowView drv = xgrvTreat.GetFocusedRow() as DataRowView;
                string serialNo = drv.Row["流水号"].ToString();
                string executeStatus = drv.Row["执行状态"].ToString();
                // string chargeIndic = drv.Row["收费标识"].ToString();
                string category = drv.Row["项目类别"].ToString();
                string execute_no = drv.Row["治疗编号"].ToString();
                //if (chargeIndic.Equals("未收费"))
                //{
                //    XtraMessageBox.Show("当前项目未收费,不可以补执行!", "系统提示!");
                //    return;
                //}
                if (executeStatus.Equals("已执行"))
                {
                    XtraMessageBox.Show("选中已执行,不能重复执行!", "系统提示!");
                    return;
                }
                if (executeStatus.Equals("取消执行"))
                {
                    XtraMessageBox.Show("选中已取消执行,不可再执行!", "系统提示!");
                    return;
                }
                if (executeStatus.Equals("退费"))
                {
                    XtraMessageBox.Show("选中已退费,不可执行!", "系统提示!");
                    return;
                }
                // string exeDept = ste.CheckTreatPerfor(serialNo);

                //if (!category.Equals("治疗"))
                //{
                //    XtraMessageBox.Show("当前医嘱不可以在非治疗类被执行!", "系统提示!");
                //    return;
                //}
                ArrayList arrayList1 = new ArrayList();

                arrayList1 = ste.ExecuteOrdersList(execute_no, barStaticItem7.Caption.ToString(), execute_nures_id);

                if (arrayList1!=null)
                {

                    bool suc = ste.UpdateArrayList(arrayList1);
                    if (suc)
                    {
                        XtraMessageBox.Show("执行成功!", "系统提示!");

                        xgrvPats_SelectionChanged(null, null);
                    }
                }
                //DataSet row1 = ste.GetexNorow(execute_no);

                //if (row1.Tables[0].Rows.Count > 0)
                //{
                //    DataSet rows1 = ste.GetexRows(row1.Tables[0].Rows[0]["PATIENT_ID"].ToString(), row1.Tables[0].Rows[0]["SCHEDULE_PERFORM_TIME"].ToString());
                //    ArrayList arrayList1 = new ArrayList();
                //    if (rows1.Tables[0].Rows.Count > 0)
                //    {
                //        foreach (DataRow dr in rows1.Tables[0].Rows)
                //        {
                //                    string sql1 = "UPDATE OUTPDOCT.TRANS_ORDERS_EXECUTE_NEW_2 SET EXECUTE_STATUS='1',EXECUTE_TIME=" + SqlManager.GetOraDbDate_LONG(DateTime.Now) +
                //            ",EXECUTE_NURSE=" + SqlManager.SqlConvert(barStaticItem7.Caption.ToString()) +
                //            ",EXECUTE_NURSE_NO=" + SqlManager.SqlConvert(execute_nures_id) +
                //             " WHERE EXECUTE_NO=" + SqlManager.SqlConvert(dr["EXECUTE_NO"].ToString());
                //                    arrayList1.Add(sql1);
                //                    string sql2 = "UPDATE OUTP_TREAT_REC SET EXECUTE_STATUS='0',EXECUTE_TIME=" + SqlManager.GetOraDbDate_LONG(DateTime.Now)
                //           + ",EXECUTE_USER_NAME=" + SqlManager.SqlConvert(execute_nures_id)
                //           + " WHERE SERIAL_NO=" + SqlManager.SqlConvert(dr["SERIAL_NO"].ToString());
                //                    arrayList1.Add(sql2);
                //        }
                //        bool suc = ste.UpdateArrayList(arrayList1);
                //        if (suc)
                //        {
                //            XtraMessageBox.Show("执行成功!", "系统提示!");

                //            xgrvPats_SelectionChanged(null, null);
                //        }
                //    }

                //}
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("请联系管理员!", "系统提示!");
            }
        }
        #endregion

        #region 取消执行医嘱
        private void barUnExecute_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (xgrvTreat.RowCount <= 0) return;
                DataRowView drv = xgrvTreat.GetFocusedRow() as DataRowView;
                string serialNo = drv.Row["流水号"].ToString();
                string executeStatus = drv.Row["执行状态"].ToString();
                string chargeIndic = drv.Row["收费标识"].ToString();
                string category = drv.Row["类别"].ToString();
                string itemNo = drv.Row["项目序号"].ToString();
                string executeNo = drv.Row["治疗编号"].ToString();
                if (chargeIndic.Equals("未收费"))
                {
                    XtraMessageBox.Show("当前项目未收费,不可以取消执行!", "系统提示!");
                    return;
                }
                if (executeStatus.Equals("取消执行"))
                {
                    XtraMessageBox.Show("选中已取消执行,不可再执行!", "系统提示!");
                    return;
                }
                string exeDept = ste.CheckTreatPerfor(serialNo);
                if (!category.Equals("治疗"))
                {
                    XtraMessageBox.Show("当前医嘱不可以在非治疗类被取消执行!", "系统提示!");
                    return;
                }
                ArrayList arrayList = new ArrayList();

                arrayList = ste.CancelExecuteOrders(serialNo,itemNo,executeNo,barStaticItem7.Caption.ToString(), execute_nures_id);

                //string sql = "UPDATE OUTP_TREAT_REC SET EXECUTE_STATUS='1',EXECUTE_TIME=" + SqlManager.GetOraDbDate_Short(DateTime.Now)
                //    + ",EXECUTE_USER_NAME=" + SqlManager.SqlConvert(SystemParm.LoginUser.EMP_NO)
                //    + " WHERE SERIAL_NO=" + SqlManager.SqlConvert(serialNo);
                //arrayList.Add(sql);
                bool suc = ste.UpdateArrayList(arrayList);
                if (suc)
                {
                    XtraMessageBox.Show("取消执行成功!", "系统提示!");
                    xgrvPats_SelectionChanged(null, null);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("请联系管理员!", "系统提示!");
            }
        }
        #endregion

        #region 病人信息双击

        private void xgrdPats_DoubleClick(object sender, EventArgs e)
        {
            //try
            //{
            //    ParentForm frm = TJHIS.Base.ParentForm.GetFormInDll("TJHIS_OUTPNURSE_TransfusionW.dll", "TJHIS.OUTPNURSE.TransfusionW.TransfusionFrm2");
            //    frm.PatientInfo = xgrvPats.GetFocusedDataRow();
            //    TJHIS.Base.MainFrm.Instance.openForm(frm, string.Empty);
            //}
            //catch (Exception ex)
            //{
            //    MsgHelper.ShowInfo(ex.Message);
            //    His00Log.Write(ex.ToString());
            //}
        }
        #endregion

        #region 行样式
        private void xgrvPats_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
            try
            {
                int hand = e.RowHandle;
                if (hand < 0) return;
                DataRow dr = this.xgrvPats.GetDataRow(hand);
                if (dr == null) return;
                if (dr["皮试标志"].ToString().Equals("是"))
                {
                    e.Appearance.ForeColor = System.Drawing.Color.Red;// 改变行字体颜色                     
                }
            }
            catch (Exception) { }
        }
        #endregion

        #region 查找病人

        private void barQuery_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {

                if (GVars.DEP_POWER)
                {
                    dsPats = ste.GetPats_All("*", (DateTime)barDateEdit1.EditValue, (DateTime)barDateEdit2.EditValue, GVars.DEP_EXCEPT);
                }
                else
                {
                    dsPats = ste.GetPats_All(GVars.Ward_ID, (DateTime)barDateEdit1.EditValue, (DateTime)barDateEdit2.EditValue, GVars.DEP_EXCEPT);
                }
                
                xgrdPats.DataSource = dsPats.Tables[0];
                xgrvPats.Columns["CLINIC_NO"].Visible = false;
                xgrvPats.Columns["DATE_OF_BIRTH"].Visible = false;
                xgrvPats.Columns["性别"].Width = 40;
                xgrvPats.Columns["年龄"].Width = 40;
                if (dsPats.Tables[0].Rows.Count > 0)
                {
                    xgrvPats.SelectRow(0);
                }
                else
                {
                    xgrdTreat.DataSource = null;
                    xgrdCost.DataSource = null;
                    lblID.Text = "";
                    lblName.Text ="";
                    lblSex.Text = "";
                    lblBirthday.Text = "";
                    label6.Text = "";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, ex.Message, this.GetType().Name);

            }
        }
        #endregion

        #region 修改执行时间 
        //private void barModifyDate_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        //{
        //    try
        //    {
        //        if (xgrvTreat.RowCount <= 0) return;
        //        DataRowView drv = xgrvTreat.GetFocusedRow() as DataRowView;
        //        string executeDate = drv.Row["执行时间"].ToString();
        //        string serailNo = drv.Row["SERIAL_NO"].ToString();
        //        // string subNo = drv.Row["SUB_NO"].ToString();
        //        if (!string.IsNullOrEmpty(executeDate))
        //        {
        //            frmModifyDate frm = new frmModifyDate();
        //            frm.dtExecuteTime = executeDate;
        //            if (frm.ShowDialog() == DialogResult.OK)
        //            {
        //                drv.Row["执行时间"] = frm.dtExecuteTime;
        //                ArrayList arrayList = new ArrayList();

        //                arrayList = ste.ReviseExecuteTime(frm.dtExecuteTime, serailNo);

        //                //string sql = "UPDATE OUTP_TREAT_REC SET EXECUTE_TIME= " + SqlManager.GetOraDbDate_Short(frm.dtExecuteTime)
        //                //   + " WHERE SERIAL_NO=" + SqlManager.SqlConvert(serailNo);
        //                //arrayList.Add(sql);
        //                bool suc = ste.UpdateArrayList(arrayList);
        //                if (suc)
        //                {
        //                    XtraMessageBox.Show("修改成功!", "系统提示!");
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show(ex.Message);
        //        His00Log.Write(ex.ToString());
        //    }
        //}
        #endregion





        private void barEditItem1_EditValueChanged(object sender, EventArgs e)
        {
            if ((barEditItem1.EditValue.ToString()).Length == 4 || (barEditItem1.EditValue.ToString()).Length == 3)
            {



                DataSet ff = ste.GetNurse(barEditItem1.EditValue.ToString());

                barStaticItem7.Caption = ff.Tables[0].Rows[0]["NAME"].ToString();
                execute_nures_id = ff.Tables[0].Rows[0]["EMP_NO"].ToString();


                barEditItem1.EditValue = "";

            }
        }

        private void textEdit_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.KeyCode == Keys.Enter)
                {
                    if ( barCheckItem1.Checked==true)
                    {
                        if (dsTreat == null)
                        {
                            XtraMessageBox.Show("患者列表没有数据，请更改查询日期后进行查询","系统提示");
                            return;

                        }
                        
                        if (dsTreat.Tables[0].Rows.Count <= 0)
                        {
                            textEdit.Text = "";
                            return;
                        }
                        int ff = 0;
                        XtraReportHelper.PrinterName = PrinterManager.GetPrinterName(_PatientTemplate); //设置打印机
                        foreach (DataRow dc in dsTreat.Tables[0].Rows)
                        {
                            if (dc["当天执行次数"].ToString().Equals("1"))
                            {
                                string which_day1 = dc["第几天"].ToString();
                                string item_name1 = dc["项目名称"].ToString();
                                string abidance1 = dc["共几天"].ToString();
                                string execute_NO = dc["治疗编号"].ToString();
                                string curr_count=dc["当前执行次数"].ToString();
                                string total=dc["执行总次数"].ToString();
                                string patient_name1 = lblName.Text;
                               // string printName = GVars.IniFile.ReadString("PRINTER", "INJECTION_LIST", string.Empty);
                               // PrintTool.SetDefaultPrint(printName);
                                if (int.Parse(dc["第几天"].ToString())>ff)
                                {
                                    ExcelTemplatePrint1(patient_name1, item_name1, which_day1, abidance1, execute_NO,curr_count,total);
                                    ff = int.Parse(dc["第几天"].ToString());
                                }
                               
                            }
                        }
                        textEdit.Text = "";
                    }
                    else if(textEdit.Text.Length<10)
                    {
                        if (textEdit.Text.Length >=3 )
                        {
                            string exno = textEdit.Text;

                            DataSet row1 = ste.GetexNorow(exno);

                            bool suc = ste.BachExecute(row1, barStaticItem7.Caption.ToString(),execute_nures_id);
                            if (suc)
                            {
                                XtraMessageBox.Show("执行成功!", "系统提示!");

                            }
                      
                            textEdit.Text = "";
                        }
                    }
                   
                }
                
                
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, ex.Message, this.GetType().Name);

            }

        }

        private void ExcelTemplatePrint1(string patient_name, string item_name, string which_day, string abidance,string execute_no,string curr_count,string total)
        {


            #region 旧版打印

            // 读取模板文件
            //string strExcelTemplateFile = System.IO.Path.Combine(Application.StartupPath, "Template\\治疗单执行码.xls");

            //excelAccess.Open(strExcelTemplateFile);				//用模板文件
            //excelAccess.IsVisibledExcel = true;
            //excelAccess.FormCaption = string.Empty;
            //Hashtable colPos = new Hashtable();

            //string iniFile = System.IO.Path.Combine(Application.StartupPath, "Template\\治疗单执行码.ini");
            //if (System.IO.File.Exists(iniFile) == true)
            //{
            //    StreamReader sr = new StreamReader(iniFile);

            //    string line = string.Empty;
            //    int row = 0;
            //    int col = 0;
            //    string fieldName = string.Empty;
            //    string fieldValue = string.Empty;

            //    while ((line = sr.ReadLine()) != null)
            //    {
            //        // 获取配置
            //        fieldName = ExcelAccess.GetConfigParts(line, ref row, ref col);

            //        switch (fieldName)
            //        {
            //            case "EXECUTE_NO":           // 执行编号
            //                excelAccess.SetCellText(row, col,"*"+execute_no+"*");
            //                continue;
            //            case "PATIENT_NAME":          //患者姓名
            //                excelAccess.SetCellText(row, col, patient_name);
            //                continue;

            //            case "ITEM_NAME":          //项目名称
            //                excelAccess.SetCellText(row, col, item_name);
            //                continue;
            //            case "WHICH_DAY":          //第几天
            //                excelAccess.SetCellText(row, col, which_day);
            //                continue;
            //            case "ABIDANCE":          //共几天
            //                excelAccess.SetCellText(row, col, abidance);
            //                continue;

            //        }

            //    }
            //}
            //excelAccess.Print();

            //excelAccess.Close(false);

            #endregion


            #region 新版打印
            List<Parameter> li = new List<Parameter>();
            //添加对应的参数
            Parameter p = new Parameter();
            p.Name = "NAME";//姓名
            p.Value = patient_name;
            li.Add(p);

            p = new Parameter();
            p.Name = "ITEM_NAME";//项目名称
            p.Value = item_name;
            li.Add(p);

            p = new Parameter();
            p.Name = "AGE";//年龄
            p.Value = lblBirthday.Text;
            li.Add(p);

            p = new Parameter();
            p.Name = "WHICH_DAY";//第几天
            p.Value = which_day;
            li.Add(p);

            p = new Parameter();
            p.Name = "EXECUTE_NO";//执行单号
            p.Value = execute_no;
            li.Add(p);

            p = new Parameter();
            p.Name = "ABIDANCE";//共几天
            p.Value = abidance;
            li.Add(p);

            p = new Parameter();
            p.Name = "CURR_COUNT";//第几次
            p.Value = curr_count;
            li.Add(p);

            p = new Parameter();
            p.Name = "TOTAL";//共几次
            p.Value = total;
            li.Add(p);


            DataSet dsPrint = new DataSet();
            DataTable dt = new DataTable();
            dsPrint.Tables.Add(dt);     //该打印没有使用结果集，定义一个空结果集
            XtraReportHelper.PrinterName = PrinterManager.GetPrinterName(_PatientTemplate); //设置打印机
            XtraReportHelper.Print(_PatientTemplate, dsPrint, li, this.AppCode); //调用打印函数            
            #endregion
        }

        private void xgrvTreat_RowStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowStyleEventArgs e)
        {
          

        }

        /// <summary>
        /// 查找定位病人
        /// </summary>
        private void SelectPatientRow()
        {
            string pid = barCardNo.EditValue.ToString();
            if (DataSetHelper.HasRecord(dsPats) && !string.IsNullOrEmpty(pid))
            {
                DataRow[] drs = dsPats.Tables[0].Select("病人ID = '" + pid + "'");
                if (drs.Length > 0)
                {
                    int location = dsPats.Tables[0].Rows.IndexOf(drs[0]);
                    xgrvPats.SelectRow(location);
                }
            }
        }
        #region 读卡模块
        private CardReadManager cardRead = new CardReadManager();  //读卡管理类
        private bool useHealthyCard = false;  //是否使用电子健康卡
        /// <summary>
        /// 设置卡按钮状态
        /// </summary>
        private void SetCardButton()
        {
            //身份证
            if (dic.ContainsKey("READ_ID_NO_TYPE") && dic["READ_ID_NO_TYPE"] != "00")
            {
                barIDCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barIDCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            //院内卡
            if (dic.ContainsKey("READ_CARD_NO_TYPE") && dic["READ_CARD_NO_TYPE"] != "00")
            {
                barHospitalCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barHospitalCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            //电子健康卡
            if (dic.ContainsKey("READ_HEALTH_NO_TYPE") && dic["READ_HEALTH_NO_TYPE"] != "00")
            {
                barHealthCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barHealthCard.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
        }
        /// <summary>
        /// 开放刷卡按钮
        /// </summary>
        private void OpenButton()
        {
            barHospitalCard.Enabled = true;
            barIDCard.Enabled = true;
            barHealthCard.Enabled = true;
            barInsuranceCard.Enabled = true;
        }
        /// <summary>
        /// 禁用刷卡按钮
        /// </summary>
        private void CloseButton()
        {
            barHospitalCard.Enabled = false;
            barIDCard.Enabled = false;
            barHealthCard.Enabled = false;
            barInsuranceCard.Enabled = false;
        }
        /// <summary>
        /// 读诊疗卡
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barPaCard_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                CloseButton();
                string rt = cardRead.ReadCard("院内卡", dic["READ_CARD_NO_TYPE"]);
                if (string.IsNullOrEmpty(rt))
                {
                    OpenButton();
                    return;
                }
                //根据诊疗卡获取病人ID
                string txtPID = cardRead.GetPatientIDFromCardNo(rt);

                barCardNo.EditValue = txtPID;
                SelectPatientRow();
                OpenButton();
            }
            catch (Exception)
            {
                OpenButton();
                throw;
            }
        }
        /// <summary>
        /// 读身份证
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barIDCard_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                CloseButton();
                string rt = cardRead.ReadCard("身份证", dic["READ_ID_NO_TYPE"]);
                if (string.IsNullOrEmpty(rt))
                {
                    OpenButton();
                    return;
                }
                //根据身份证获取病人ID
                string txtPID = cardRead.GetPatientIDFromIdNo(rt);

                barCardNo.EditValue = txtPID;
                SelectPatientRow();
                OpenButton();
            }
            catch (Exception)
            {
                OpenButton();
                throw;
            }
        }
        /// <summary>
        /// 读银行卡
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barBankCard_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                CloseButton();
                string rt = cardRead.ReadCard("健康卡", dic["READ_HEALTH_NO_TYPE"]);
                if (string.IsNullOrEmpty(rt))
                {
                    OpenButton();
                    return;
                }
                //根据银行卡获取病人ID
                string txtPID = cardRead.GetPatientIDFromBankNo(rt);

                barCardNo.EditValue = txtPID;
                SelectPatientRow();
                OpenButton();
            }
            catch (Exception)
            {
                OpenButton();
                throw;
            }
        }
        /// <summary>
        /// 读社保卡
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void barSSCard_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                CloseButton();
                string rt = cardRead.ReadCard("医保卡", null);
                if (string.IsNullOrEmpty(rt))
                {
                    OpenButton();
                    return;
                }
                //根据社保卡获取病人ID,第3代社保卡号就是身份证号；如果是其他旧社保卡可根据实际情况查询对应的数据
                string txtPID = cardRead.GetPatientIDFromIdNo2(rt);

                barCardNo.EditValue = txtPID;
                SelectPatientRow();
                OpenButton();
            }
            catch (Exception)
            {
                OpenButton();
                throw;
            }
        }
        #endregion
        /// <summary>
        /// Shown事件，窗体加载完执行
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void frmTreatExecute_Shown(object sender, EventArgs e)
        {
            //设定读卡状态
            dic = ReadIdentityCardBusiness.GetCardParameter(this.AppCode);
            SetCardButton();
        }

        private void barEditItem4_EditValueChanged(object sender, EventArgs e)
        {
            if (barEditItem4.EditValue.ToString() == "") return;
           DataSet ds= ste.ExecuteCount(barEditItem4.EditValue.ToString());


            if (ds.Tables[0].Rows.Count < 1)
            {
                XtraMessageBox.Show("没有查询到相关的处置类信息，请联系技术人员！","系统提示");
                return;
            }
            if (ds.Tables[0].Rows[0]["EXECUTE_STATUS"].ToString().Equals("1"))
            {
                XtraMessageBox.Show("当前处置已被执行，,不能重复执行！","系统提示");

                barEditItem4.EditValue = "";
                return;
            }

            if (ds.Tables[0].Rows[0]["EXECUTE_STATUS"].ToString().Equals("3"))
            {
                XtraMessageBox.Show("当前处置已退费，,不可执行！", "系统提示");

                barEditItem4.EditValue = "";
                return;
            }

            try 
            {
                ArrayList arrayList1 = new ArrayList();

                arrayList1 = ste.ExecuteOrdersList(barEditItem4.EditValue.ToString(), barStaticItem7.Caption.ToString(), execute_nures_id);

                if (arrayList1 != null)
                {

                    bool suc = ste.UpdateArrayList(arrayList1);
                    if (suc)
                    {
                        XtraMessageBox.Show("执行成功!", "系统提示");

                        xgrvPats_SelectionChanged(null, null);
                        barEditItem4.EditValue = "";

                    }
                }

            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("执行失败，请联系技术人员！", "系统提示");
            }
        }
        #region 退费

        private void barRefund_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try 
            {
                if (xgrvTreat.RowCount <= 0) return;
                DataRowView drv = xgrvTreat.GetFocusedRow() as DataRowView;
                string serialNo = drv.Row["流水号"].ToString();
                string executeStatus = drv.Row["执行状态"].ToString();
                // string chargeIndic = drv.Row["收费标识"].ToString();
                string category = drv.Row["项目类别"].ToString();
                string execute_no = drv.Row["治疗编号"].ToString();
                //if (chargeIndic.Equals("未收费"))
                //{
                //    XtraMessageBox.Show("当前项目未收费,不可以补执行!", "系统提示!");
                //    return;
                //}
                if (executeStatus.Equals("已执行"))
                {
                    XtraMessageBox.Show("选中已执行,不能执行退费!", "系统提示!");
                    return;
                }
                if (executeStatus.Equals("退费"))
                {
                    XtraMessageBox.Show("选中已退费,不可再执行!", "系统提示!");
                    return;
                }
                // string exeDept = ste.CheckTreatPerfor(serialNo);

                //if (!category.Equals("治疗"))
                //{
                //    XtraMessageBox.Show("当前医嘱不可以在非治疗类被执行!", "系统提示!");
                //    return;
                //}
                ArrayList arrayList1 = new ArrayList();

                arrayList1 = ste.RefundOrdersList(execute_no, barStaticItem7.Caption.ToString(), execute_nures_id);

                if (arrayList1 != null)
                {

                    bool suc = ste.UpdateArrayList(arrayList1);
                    if (suc)
                    {
                        XtraMessageBox.Show("执行成功!", "系统提示!");

                        xgrvPats_SelectionChanged(null, null);
                    }
                }

            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("退费失败，请联系技术人员！","系统提示");
            }
        }

        #endregion

        private void xgrvTreat_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            DataRowView drv = xgrvTreat.GetFocusedRow() as DataRowView;//获取当前行
            if (drv != null)
            {
             //string   currTreatSerialNo = drv.Row["流水号"].ToString();    //流水号
             //string   currTreatItemNo = drv.Row["项目编号"].ToString();        //诊疗项目医嘱序号
             //string   currTreatItemClass = drv.Row["ITEM_CLASS"].ToString();  //诊疗项目类别
             //string   currClinicSerialNo = drv.Row["CLINIC_SERIAL_NO"].ToString();//诊疗项目流水号

                DataSet dsCost = ste.GetOrdersCosts(drv.Row["就诊时间"].ToString(), drv.Row["就诊号"].ToString(), drv.Row["患者ID"].ToString(), drv.Row["流水号"].ToString(), drv.Row["项目编号"].ToString());

                if (dsCost.Tables[0].Rows.Count == 0)
                {
                    xgrdCost.DataSource = null;
                    return;
                }
                xgrdCost.DataSource = dsCost.Tables[0];
                //按照诊疗项目过滤显示费用项目
                //if (dsTreat != null)
                //{
                //    dsTreat.Tables[0].DefaultView.RowFilter = "SERIAL_NO='" + currTreatSerialNo + "' AND ORDER_NO='" + currTreatItemNo + "'";
                //    dsTreat.Tables[0].DefaultView.Sort = "ITEM_NO ASC"; //按顺序号排序

                //    ////选中最后一行
                //    //if (dsCostItem != null && dsCostItem.Tables.Count > 0 && dsCostItem.Tables[0].Rows.Count > 0)
                //    //{
                //    //    xgrvOrdersCosts.FocusedRowHandle = xgrvOrdersCosts.RowCount - 1;
                //    //}
                //}

            }
            else
            {
                xgrdCost.DataSource = null;
                //currTreatSerialNo = null;
                //dsTreat.Tables[0].DefaultView.RowFilter = "1=2";//不显示结果集
            }
        }

        private void barBtnExecute_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            barExecute.PerformClick();
        }

        private void barBtnRefound_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            barRefund.PerformClick();
        }
    }
}
